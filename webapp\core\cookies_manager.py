"""
YouTube Cookies管理器
负责cookies的导入、验证、备份和管理
"""

import os
import json
import logging
import subprocess
import shutil
import glob
from datetime import datetime
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

class CookiesManager:
    """YouTube Cookies管理器"""
    
    def __init__(self):
        # 使用绝对路径，确保在容器中正确工作
        self.cookies_file = os.path.abspath('webapp/config/universal_cookies.txt')  # 改为通用cookies文件
        self.config_dir = os.path.abspath('webapp/config')
        self.backup_pattern = 'universal_cookies_backup_*.txt'

        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)

        # 平台配置：定义各平台的域名和重要cookies
        self.platform_configs = {
            'youtube': {
                'domains': ['youtube.com', 'google.com', '.youtube.com', '.google.com'],
                'important_cookies': [
                    'SID', 'HSID', 'SSID', 'APISID', 'SAPISID', 'LOGIN_INFO', 'VISITOR_INFO1_LIVE',
                    '__Secure-1PSID', '__Secure-3PSID', '__Secure-1PAPISID', '__Secure-3PAPISID',
                    '__Secure-1PSIDCC', '__Secure-3PSIDCC', '__Secure-1PSIDTS', '__Secure-3PSIDTS',
                    'SIDCC', 'CONSENT', 'NID', 'AEC'
                ],
                'auth_cookies': ['SID', '__Secure-1PSID', '__Secure-3PSID']
            },
            'twitter': {
                'domains': ['twitter.com', 'x.com', '.twitter.com', '.x.com'],
                'important_cookies': [
                    'auth_token', 'ct0', 'guest_id', 'personalization_id', 'gt', 'twid',
                    '_twitter_sess', 'remember_checked_on', 'kdt', 'dnt', 'mbox'
                ],
                'auth_cookies': ['auth_token', 'ct0']
            },
            'instagram': {
                'domains': ['instagram.com', '.instagram.com'],
                'important_cookies': [
                    'sessionid', 'csrftoken', 'ds_user_id', 'ig_did', 'ig_nrcb'
                ],
                'auth_cookies': ['sessionid']
            },
            'tiktok': {
                'domains': ['tiktok.com', '.tiktok.com'],
                'important_cookies': [
                    'sessionid', 'sid_tt', 'uid_tt', 'sid_guard', 'uid_guard', 'ssid_ucp_v1'
                ],
                'auth_cookies': ['sessionid', 'sid_tt']
            },
            'bilibili': {
                'domains': ['bilibili.com', '.bilibili.com'],
                'important_cookies': [
                    'SESSDATA', 'bili_jct', 'DedeUserID', 'DedeUserID__ckMd5', 'sid'
                ],
                'auth_cookies': ['SESSDATA']
            }
        }

    def detect_platform_from_url(self, url: str) -> str:
        """从URL智能检测平台"""
        url_lower = url.lower()

        for platform, config in self.platform_configs.items():
            for domain in config['domains']:
                # 只移除前导点，保留域名中的点
                clean_domain = domain.lstrip('.')
                if clean_domain in url_lower:
                    return platform

        return 'unknown'

    def detect_platform_from_domain(self, domain: str) -> str:
        """从域名检测平台"""
        for platform, config in self.platform_configs.items():
            for platform_domain in config['domains']:
                if platform_domain in domain:
                    return platform
        return 'unknown'

    def get_all_important_cookies(self) -> List[str]:
        """获取所有平台的重要cookies列表"""
        all_cookies = []
        for platform, config in self.platform_configs.items():
            all_cookies.extend(config['important_cookies'])
        return list(set(all_cookies))  # 去重

    def get_platform_cookies(self, platform: str) -> List[str]:
        """获取指定平台的重要cookies"""
        if platform in self.platform_configs:
            return self.platform_configs[platform]['important_cookies']
        return []

    def analyze_cookies_by_platform(self) -> Dict:
        """按平台分析cookies"""
        platform_analysis = {}

        try:
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 6:
                            domain = parts[0]
                            cookie_name = parts[5]

                            # 检测平台
                            platform = self.detect_platform_from_domain(domain)

                            if platform != 'unknown':
                                if platform not in platform_analysis:
                                    platform_analysis[platform] = {
                                        'found_cookies': [],
                                        'important_cookies': self.get_platform_cookies(platform),
                                        'auth_cookies': self.platform_configs[platform]['auth_cookies']
                                    }

                                if cookie_name in platform_analysis[platform]['important_cookies']:
                                    platform_analysis[platform]['found_cookies'].append(cookie_name)

        except Exception as e:
            logger.error(f"分析平台cookies失败: {e}")

        # 计算每个平台的完整性
        for platform, data in platform_analysis.items():
            data['found_cookies'] = list(set(data['found_cookies']))  # 去重
            data['completeness'] = len(data['found_cookies']) / len(data['important_cookies']) if data['important_cookies'] else 0
            data['has_auth'] = any(cookie in data['found_cookies'] for cookie in data['auth_cookies'])

        return platform_analysis

    def get_cookies_for_url(self, url: str) -> str:
        """智能获取cookies文件路径 - 直接返回通用cookies文件，yt-dlp会自动处理"""
        platform = self.detect_platform_from_url(url)

        if not os.path.exists(self.cookies_file):
            logger.warning(f"⚠️ Cookies文件不存在: {self.cookies_file}")
            return self.cookies_file

        # 检查是否有该平台的cookies
        platform_analysis = self.analyze_cookies_by_platform()

        if platform == 'unknown':
            logger.info(f"🔍 未识别平台，使用通用cookies文件: {url}")
        elif platform in platform_analysis:
            analysis = platform_analysis[platform]
            if analysis['has_auth']:
                logger.info(f"🎯 检测到 {platform} 平台，cookies包含认证信息: {url}")
            else:
                logger.warning(f"⚠️ 检测到 {platform} 平台，但缺少认证cookies，可能影响下载: {url}")
        else:
            logger.warning(f"⚠️ 检测到 {platform} 平台，但cookies中无此平台数据: {url}")

        # 始终返回通用cookies文件，让yt-dlp自动处理不同平台的cookies
        return self.cookies_file

    def get_status(self) -> Dict:
        """获取cookies状态"""
        try:
            if not os.path.exists(self.cookies_file):
                return {
                    'exists': False,
                    'status': 'missing',
                    'message': '通用cookies文件不存在'
                }
            
            stat = os.stat(self.cookies_file)
            file_size = stat.st_size
            modified_time = stat.st_mtime
            modified_date = datetime.fromtimestamp(modified_time)
            
            # 分析cookies内容
            important_cookies = self._analyze_cookies()
            platform_analysis = self.analyze_cookies_by_platform()

            # 判断状态
            days_since_modified = (datetime.now() - modified_date).days
            
            if days_since_modified > 300:  # 10个月
                status = 'expired'
                message = 'Cookies可能已过期，建议更新'
            elif days_since_modified > 180:  # 6个月
                status = 'warning'
                message = 'Cookies需要关注，建议近期更新'
            elif len(important_cookies) < 3:
                status = 'incomplete'
                message = 'Cookies内容不完整'
            else:
                status = 'good'
                message = 'Cookies状态良好'
            
            return {
                'exists': True,
                'status': status,
                'message': message,
                'file_size': file_size,
                'modified_time': modified_time,
                'modified_date': modified_date.isoformat(),
                'days_since_modified': days_since_modified,
                'important_cookies': important_cookies,
                'platform_analysis': platform_analysis,
                'supported_platforms': list(platform_analysis.keys())
            }
            
        except Exception as e:
            logger.error(f"获取cookies状态失败: {e}")
            return {
                'exists': False,
                'status': 'error',
                'message': f'检查状态失败: {str(e)}'
            }
    
    def _get_important_cookies_list(self) -> List[str]:
        """获取重要cookies列表定义"""
        return self.get_all_important_cookies()

    def _analyze_cookies(self) -> List[str]:
        """分析cookies文件，返回找到的重要cookies"""
        important_cookies = self._get_important_cookies_list()
        found_cookies = []

        try:
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 6:
                            domain = parts[0]
                            cookie_name = parts[5]

                            # 检查是否是支持的平台域名
                            platform = self.detect_platform_from_domain(domain)
                            if platform != 'unknown' and cookie_name in important_cookies:
                                found_cookies.append(cookie_name)
                                logger.debug(f"🔍 找到重要cookie: {domain} -> {cookie_name} ({platform})")

        except Exception as e:
            logger.error(f"分析cookies失败: {e}")

        # 移除重复项并保持顺序
        found_cookies = list(dict.fromkeys(found_cookies))
        return found_cookies

    def _analyze_cookies_expiration(self, cookies_file_path: str) -> Dict:
        """分析cookies的过期时间"""
        try:
            current_time = datetime.now()
            current_timestamp = int(current_time.timestamp())

            cookies_expiration = {}
            earliest_expiry = None
            latest_expiry = None
            expired_count = 0
            valid_count = 0

            with open(cookies_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 7:
                            domain = parts[0]
                            cookie_name = parts[5]

                            # 检查是否是重要cookie
                            important_cookies = self._get_important_cookies_list()

                            platform = self.detect_platform_from_domain(domain)
                            if platform != 'unknown' and cookie_name in important_cookies:
                                try:
                                    expiry_timestamp = int(parts[4])
                                    expiry_date = datetime.fromtimestamp(expiry_timestamp)

                                    # 计算剩余时间
                                    remaining_seconds = expiry_timestamp - current_timestamp
                                    remaining_days = remaining_seconds / (24 * 3600)

                                    is_expired = remaining_seconds <= 0
                                    if is_expired:
                                        expired_count += 1
                                    else:
                                        valid_count += 1

                                    cookies_expiration[cookie_name] = {
                                        'expiry_timestamp': expiry_timestamp,
                                        'expiry_date': expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        'remaining_days': max(0, remaining_days),
                                        'remaining_seconds': max(0, remaining_seconds),
                                        'is_expired': is_expired,
                                        'domain': domain,
                                        'line_number': line_num
                                    }

                                    # 跟踪最早和最晚过期时间
                                    if earliest_expiry is None or expiry_timestamp < earliest_expiry:
                                        earliest_expiry = expiry_timestamp
                                    if latest_expiry is None or expiry_timestamp > latest_expiry:
                                        latest_expiry = expiry_timestamp

                                except (ValueError, IndexError):
                                    # 过期时间格式错误
                                    cookies_expiration[cookie_name] = {
                                        'expiry_timestamp': None,
                                        'expiry_date': '格式错误',
                                        'remaining_days': 0,
                                        'remaining_seconds': 0,
                                        'is_expired': True,
                                        'domain': domain,
                                        'line_number': line_num
                                    }
                                    expired_count += 1

            # 计算整体统计
            total_cookies = len(cookies_expiration)
            if total_cookies == 0:
                return {
                    'total_cookies': 0,
                    'valid_cookies': 0,
                    'expired_cookies': 0,
                    'cookies_details': {},
                    'overall_status': 'no_cookies',
                    'earliest_expiry': None,
                    'latest_expiry': None,
                    'min_remaining_days': 0,
                    'max_remaining_days': 0
                }

            # 计算剩余时间范围
            valid_cookies_details = {k: v for k, v in cookies_expiration.items() if not v['is_expired']}
            if valid_cookies_details:
                min_remaining_days = min(v['remaining_days'] for v in valid_cookies_details.values())
                max_remaining_days = max(v['remaining_days'] for v in valid_cookies_details.values())
            else:
                min_remaining_days = 0
                max_remaining_days = 0

            # 判断整体状态
            if expired_count == total_cookies:
                overall_status = 'all_expired'
            elif expired_count > total_cookies * 0.5:
                overall_status = 'mostly_expired'
            elif min_remaining_days < 30:
                overall_status = 'expiring_soon'
            elif min_remaining_days < 90:
                overall_status = 'good'
            else:
                overall_status = 'excellent'

            return {
                'total_cookies': total_cookies,
                'valid_cookies': valid_count,
                'expired_cookies': expired_count,
                'cookies_details': cookies_expiration,
                'overall_status': overall_status,
                'earliest_expiry': datetime.fromtimestamp(earliest_expiry).strftime('%Y-%m-%d %H:%M:%S') if earliest_expiry else None,
                'latest_expiry': datetime.fromtimestamp(latest_expiry).strftime('%Y-%m-%d %H:%M:%S') if latest_expiry else None,
                'min_remaining_days': min_remaining_days,
                'max_remaining_days': max_remaining_days
            }

        except Exception as e:
            logger.error(f"分析cookies过期时间失败: {e}")
            return {
                'total_cookies': 0,
                'valid_cookies': 0,
                'expired_cookies': 0,
                'cookies_details': {},
                'overall_status': 'error',
                'error': str(e)
            }

    def _compare_cookies_before_after(self, before_cookies: List[str], after_cookies: List[str], backup_cookies: List[str]) -> Dict:
        """对比恢复前后的cookies变化"""
        before_set = set(before_cookies)
        after_set = set(after_cookies)
        backup_set = set(backup_cookies)

        added = list(after_set - before_set)  # 新增的cookies
        lost = list(before_set - after_set)   # 丢失的cookies
        kept = list(before_set & after_set)   # 保持的cookies

        # 检查是否按预期恢复
        expected_from_backup = list(backup_set)
        actually_restored = list(after_set)

        return {
            'added': added,
            'lost': lost,
            'kept': kept,
            'expected_from_backup': expected_from_backup,
            'actually_restored': actually_restored,
            'restoration_success': backup_set == after_set,
            'improvement': len(after_cookies) > len(before_cookies)
        }

    def test_cookies(self) -> Dict:
        """测试cookies有效性"""
        try:
            if not os.path.exists(self.cookies_file):
                return {
                    'success': False,
                    'valid': False,
                    'error': 'Cookies文件不存在'
                }

            # 首先检查cookies文件内容
            logger.info(f"🔍 检查cookies文件: {self.cookies_file}")
            try:
                with open(self.cookies_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    logger.info(f"📄 Cookies文件大小: {len(content)} 字符")

                    # 分析cookies内容
                    lines = content.split('\n')
                    valid_lines = [line for line in lines if line.strip() and not line.startswith('#')]
                    logger.info(f"📊 有效cookies行数: {len(valid_lines)}")

                    # 检查重要cookies
                    important_cookies = self._analyze_cookies()
                    logger.info(f"🔑 找到重要cookies: {important_cookies}")

                    if not important_cookies:
                        return {
                            'success': True,
                            'valid': False,
                            'message': 'Cookies文件中缺少必要的YouTube认证cookies',
                            'error': '请确保cookies包含SID、HSID、SSID等重要字段'
                        }

            except Exception as e:
                logger.error(f"读取cookies文件失败: {e}")
                return {
                    'success': False,
                    'valid': False,
                    'error': f'无法读取cookies文件: {str(e)}'
                }

            # 使用yt-dlp测试YouTube视频
            test_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
            cmd = [
                'yt-dlp', '--cookies', self.cookies_file,
                '--dump-json', '--no-warnings', '--no-check-certificate',
                '--verbose',  # 添加详细输出
                test_url
            ]

            logger.info(f"🧪 测试cookies命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            logger.info(f"🧪 测试结果返回码: {result.returncode}")
            if result.stdout:
                logger.info(f"🧪 测试标准输出: {result.stdout[:300]}...")
            if result.stderr:
                logger.info(f"🧪 测试错误输出: {result.stderr[:500]}...")

            if result.returncode == 0:
                # 检查是否成功获取到视频信息
                try:
                    import json
                    video_info = json.loads(result.stdout)
                    if video_info.get('title'):
                        logger.info(f"✅ 成功获取视频信息: {video_info.get('title', 'Unknown')}")
                        return {
                            'success': True,
                            'valid': True,
                            'message': f'Cookies有效，成功获取视频: {video_info.get("title", "Unknown")}'
                        }
                except:
                    pass

                return {
                    'success': True,
                    'valid': True,
                    'message': 'Cookies有效，可以正常下载YouTube视频'
                }
            else:
                # 详细分析错误信息
                error_msg = result.stderr

                if 'Sign in to confirm you\'re not a bot' in error_msg:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies可能已过期，遇到bot检测',
                        'error': 'YouTube要求重新认证，请更新cookies'
                    }
                elif 'Private video' in error_msg or 'Video unavailable' in error_msg:
                    # 如果是视频不可用，尝试另一个测试URL
                    logger.info("🔄 测试视频不可用，尝试另一个URL...")
                    return self._test_with_alternative_url()
                elif 'HTTP Error 403' in error_msg:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies可能已过期或无效',
                        'error': 'YouTube返回403错误，请更新cookies'
                    }
                else:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies测试失败',
                        'error': error_msg[:300] if error_msg else '未知错误'
                    }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'valid': False,
                'error': '测试超时（60秒）'
            }
        except Exception as e:
            logger.error(f"测试cookies失败: {e}")
            return {
                'success': False,
                'valid': False,
                'error': str(e)
            }

    def _test_with_alternative_url(self) -> Dict:
        """使用备用URL测试cookies"""
        try:
            # 使用一个更简单的测试：获取YouTube首页
            test_url = 'https://www.youtube.com/watch?v=jNQXAC9IVRw'  # 另一个测试视频
            cmd = [
                'yt-dlp', '--cookies', self.cookies_file,
                '--dump-json', '--no-warnings', '--no-check-certificate',
                test_url
            ]

            logger.info(f"🔄 备用测试命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=45)

            if result.returncode == 0:
                return {
                    'success': True,
                    'valid': True,
                    'message': 'Cookies有效（备用测试通过）'
                }
            else:
                return {
                    'success': True,
                    'valid': False,
                    'message': 'Cookies可能无效',
                    'error': f'备用测试也失败: {result.stderr[:200] if result.stderr else "未知错误"}'
                }

        except Exception as e:
            logger.error(f"备用测试失败: {e}")
            return {
                'success': True,
                'valid': False,
                'message': 'Cookies测试失败',
                'error': f'备用测试异常: {str(e)}'
            }
    
    def import_cookies(self, cookies_content: str, format_type: str = 'auto', full_backup: bool = False) -> Dict:
        """导入cookies"""
        try:
            if not cookies_content.strip():
                return {
                    'success': False,
                    'error': '请提供cookies内容'
                }

            # 备份现有cookies（包括原始格式）
            backup_file = self._create_backup_with_original(cookies_content, format_type)

            # 检测和转换格式
            final_content, detected_format = self._convert_cookies_format(cookies_content, format_type, full_backup)

            # 验证cookies内容
            validation_result = self._validate_cookies_content(final_content)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error'],
                    'found_cookies': validation_result.get('found_cookies', [])
                }

            # 保存新cookies
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                f.write(final_content)

            logger.info(f"✅ 新cookies已保存，包含 {len(validation_result['found_cookies'])} 个重要cookies")

            # 测试新cookies
            test_result = self.test_cookies()

            return {
                'success': True,
                'message': f'Cookies导入成功 ({"完整备份" if full_backup else "智能提取"}模式)',
                'detected_format': detected_format,
                'found_cookies': validation_result['found_cookies'],
                'backup_file': backup_file,
                'test_result': test_result,
                'import_mode': 'full_backup' if full_backup else 'smart_extract'
            }

        except Exception as e:
            logger.error(f"导入cookies失败: {e}")
            return {
                'success': False,
                'error': f'导入失败: {str(e)}'
            }
    
    def _create_backup(self) -> Optional[str]:
        """创建cookies备份"""
        try:
            if os.path.exists(self.cookies_file):
                timestamp = int(datetime.now().timestamp())
                backup_file = f'youtube_cookies_backup_{timestamp}.txt'
                backup_path = os.path.join(self.config_dir, backup_file)
                shutil.copy(self.cookies_file, backup_path)
                logger.info(f"已备份现有cookies到: {backup_file}")
                return backup_file
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
        return None

    def _create_backup_with_original(self, original_content: str, format_type: str) -> Optional[str]:
        """创建包含原始格式的备份"""
        try:
            # 先备份当前的cookies文件（如果存在）
            current_backup = self._create_backup()

            # 创建原始格式的备份
            timestamp = int(datetime.now().timestamp())
            original_backup_file = f'youtube_cookies_original_{timestamp}.json'
            original_backup_path = os.path.join(self.config_dir, original_backup_file)

            # 保存原始内容和元数据
            backup_data = {
                'timestamp': timestamp,
                'format_type': format_type,
                'original_content': original_content,
                'created_time': datetime.now().isoformat(),
                'note': 'Original format backup for easy restoration'
            }

            with open(original_backup_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"已创建原始格式备份: {original_backup_file}")
            return current_backup

        except Exception as e:
            logger.error(f"创建原始格式备份失败: {e}")
            return self._create_backup()  # 降级到普通备份
    
    def _convert_cookies_format(self, content: str, format_type: str, full_backup: bool = False) -> Tuple[str, str]:
        """转换cookies格式 - 支持完整备份或智能提取"""
        # 检测格式
        detected_format = 'netscape'
        try:
            json.loads(content)
            detected_format = 'json'
        except:
            if content.startswith('# Netscape HTTP Cookie File'):
                detected_format = 'netscape'
            elif '\t' in content and ('youtube.com' in content or 'google.com' in content):
                detected_format = 'netscape'

        if detected_format == 'json':
            # JSON格式转换为Netscape格式
            cookies_data = json.loads(content)
            netscape_lines = [
                '# Netscape HTTP Cookie File',
                '# This is a generated file! Do not edit.',
                f'# {"Complete backup" if full_backup else "Auto-extracted Google/YouTube cookies"} for yt-dlp',
                ''
            ]

            extracted_count = 0

            if full_backup:
                # 完整备份模式：转换所有cookies
                logger.info("🔄 完整备份模式：转换所有cookies")
                for cookie in cookies_data:
                    if isinstance(cookie, dict) and 'domain' in cookie:
                        domain = cookie.get('domain', '')
                        name = cookie.get('name', '')
                        domain_flag = 'TRUE' if domain.startswith('.') else 'FALSE'
                        path = cookie.get('path', '/')
                        secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'

                        # 处理过期时间
                        expiration = cookie.get('expirationDate', 0)
                        if isinstance(expiration, float):
                            expiration = int(expiration)
                        elif expiration == 0:
                            expiration = 2147483647  # 默认远期时间

                        value = cookie.get('value', '')

                        # 构建Netscape格式行
                        line = f'{domain}\t{domain_flag}\t{path}\t{secure}\t{expiration}\t{name}\t{value}'
                        netscape_lines.append(line)
                        extracted_count += 1

                logger.info(f"🍪 完整备份：转换了 {extracted_count} 个cookies")
            else:
                # 智能提取模式：提取所有支持平台的cookies
                all_domains = []
                for platform, config in self.platform_configs.items():
                    all_domains.extend(config['domains'])
                important_cookies = self._get_important_cookies_list()

                for cookie in cookies_data:
                    if isinstance(cookie, dict) and 'domain' in cookie:
                        domain = cookie.get('domain', '')
                        name = cookie.get('name', '')

                        # 检查是否是目标域名
                        is_target_domain = any(target_domain in domain for target_domain in all_domains)

                        # 检查是否是重要cookie或者是支持的平台域名
                        platform = self.detect_platform_from_domain(domain)
                        if platform != 'unknown' and (name in important_cookies or is_target_domain):
                            domain_flag = 'TRUE' if domain.startswith('.') else 'FALSE'
                            path = cookie.get('path', '/')
                            secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'

                            # 处理过期时间
                            expiration = cookie.get('expirationDate', 0)
                            if isinstance(expiration, float):
                                expiration = int(expiration)
                            elif expiration == 0:
                                expiration = 2147483647  # 默认远期时间

                            value = cookie.get('value', '')

                            # 构建Netscape格式行
                            line = f'{domain}\t{domain_flag}\t{path}\t{secure}\t{expiration}\t{name}\t{value}'
                            netscape_lines.append(line)
                            extracted_count += 1

                            logger.info(f"✅ 提取cookie: {domain} -> {name}")

                logger.info(f"🍪 智能提取：从JSON中提取了 {extracted_count} 个Google/YouTube cookies")

                if extracted_count == 0:
                    logger.warning("⚠️ 未找到任何Google/YouTube相关cookies，切换到完整备份模式")
                    # 如果没有找到目标cookies，自动切换到完整备份
                    return self._convert_cookies_format(content, format_type, full_backup=True)

            return '\n'.join(netscape_lines), detected_format
        else:
            # 已经是Netscape格式
            return content, detected_format
    
    def _validate_cookies_content(self, content: str) -> Dict:
        """验证cookies内容 - 支持多平台域名"""
        important_cookies = self._get_important_cookies_list()

        found_cookies = []
        platform_cookies = {}

        for line in content.split('\n'):
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split('\t')
                if len(parts) >= 7:
                    domain = parts[0]
                    cookie_name = parts[5]

                    # 检测平台
                    platform = self.detect_platform_from_domain(domain)
                    if platform != 'unknown' and cookie_name in important_cookies:
                        found_cookies.append(cookie_name)

                        if platform not in platform_cookies:
                            platform_cookies[platform] = []
                        platform_cookies[platform].append(cookie_name)

        # 移除重复项
        found_cookies = list(set(found_cookies))

        # 验证逻辑：检查是否有任何平台的认证cookies
        has_auth = False
        for platform, config in self.platform_configs.items():
            if platform in platform_cookies:
                platform_found = platform_cookies[platform]
                auth_cookies = config['auth_cookies']
                if any(cookie in platform_found for cookie in auth_cookies):
                    has_auth = True
                    break

        if len(found_cookies) < 1 and not has_auth:
            return {
                'valid': False,
                'error': f'cookies内容不完整，只找到 {len(found_cookies)} 个重要cookies',
                'found_cookies': found_cookies,
                'platform_cookies': platform_cookies
            }

        return {
            'valid': True,
            'found_cookies': found_cookies,
            'platform_cookies': platform_cookies
        }
    
    def list_backups(self) -> List[Dict]:
        """列出备份文件"""
        try:
            backup_pattern = os.path.join(self.config_dir, self.backup_pattern)
            backup_files = glob.glob(backup_pattern)

            # 注意：原始格式备份文件会在检查备份时自动关联

            backups = []

            # 处理普通备份
            for file_path in backup_files:
                filename = os.path.basename(file_path)
                timestamp_str = filename.replace('youtube_cookies_backup_', '').replace('.txt', '')
                try:
                    timestamp = int(timestamp_str)
                    created_time = datetime.fromtimestamp(timestamp)
                    file_size = os.path.getsize(file_path)

                    # 检查是否有对应的原始格式备份
                    original_file = f'youtube_cookies_original_{timestamp}.json'
                    original_path = os.path.join(self.config_dir, original_file)
                    has_original = os.path.exists(original_path)

                    backups.append({
                        'filename': filename,
                        'path': file_path,
                        'created_time': created_time.isoformat(),
                        'created_time_str': created_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'file_size': file_size,
                        'has_original': has_original,
                        'original_file': original_file if has_original else None,
                        'backup_type': 'smart' if has_original else 'legacy'
                    })
                except ValueError:
                    continue

            # 按时间倒序排列
            backups.sort(key=lambda x: x['created_time'], reverse=True)
            return backups

        except Exception as e:
            logger.error(f"列出备份失败: {e}")
            return []
    
    def restore_backup(self, backup_filename: str, create_backup: bool = True) -> Dict:
        """恢复备份 - 智能恢复，优先使用原始格式

        Args:
            backup_filename: 要恢复的备份文件名
            create_backup: 是否在恢复前创建当前cookies的备份
        """
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            logger.info(f"🔄 开始智能恢复备份: {backup_filename}")
            logger.info(f"📁 备份文件路径: {backup_path}")
            logger.info(f"🎯 目标cookies文件: {self.cookies_file}")

            if not os.path.exists(backup_path):
                logger.error(f"❌ 备份文件不存在: {backup_path}")
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 检查是否有对应的原始格式备份
            timestamp_str = backup_filename.replace('youtube_cookies_backup_', '').replace('.txt', '')
            original_file = f'youtube_cookies_original_{timestamp_str}.json'
            original_path = os.path.join(self.config_dir, original_file)

            if os.path.exists(original_path):
                logger.info(f"🎯 发现原始格式备份，使用智能恢复: {original_file}")
                return self._restore_from_original(original_path, create_backup)
            else:
                logger.info(f"📄 使用传统文件恢复方式")
                return self._restore_from_file(backup_path, create_backup)

        except Exception as e:
            logger.error(f"❌ 恢复备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _restore_from_original(self, original_path: str, create_backup: bool = True) -> Dict:
        """从原始格式备份恢复 - 模拟导入过程"""
        try:
            logger.info(f"🎯 开始从原始格式恢复: {original_path}")

            # 读取原始格式备份
            with open(original_path, 'r', encoding='utf-8') as f:
                import json
                backup_data = json.load(f)

            original_content = backup_data.get('original_content', '')
            format_type = backup_data.get('format_type', 'auto')

            if not original_content:
                return {
                    'success': False,
                    'error': '原始格式备份内容为空'
                }

            logger.info(f"📄 原始格式: {format_type}")
            logger.info(f"📊 原始内容大小: {len(original_content)} 字符")

            # 获取恢复前状态
            before_cookies = []
            if os.path.exists(self.cookies_file):
                before_cookies = self._analyze_cookies()

            # 创建当前状态备份
            current_backup = None
            if create_backup:
                current_backup = self._create_backup()

            # 模拟导入过程 - 这是关键！使用完整备份模式
            logger.info("🔄 模拟导入过程（完整备份模式）...")
            import_result = self.import_cookies(original_content, format_type, full_backup=True)

            if not import_result['success']:
                return {
                    'success': False,
                    'error': f'模拟导入失败: {import_result["error"]}'
                }

            # 获取恢复后状态
            after_cookies = self._analyze_cookies()

            logger.info(f"✅ 智能恢复成功!")
            logger.info(f"📊 恢复前cookies: {before_cookies}")
            logger.info(f"📊 恢复后cookies: {after_cookies}")

            return {
                'success': True,
                'message': f'已从原始格式智能恢复 (格式: {format_type})',
                'method': 'smart_restore',
                'original_format': format_type,
                'current_backup': current_backup,
                'before_cookies': before_cookies,
                'after_cookies': after_cookies,
                'import_result': import_result
            }

        except Exception as e:
            logger.error(f"❌ 原始格式恢复失败: {e}")
            return {
                'success': False,
                'error': f'原始格式恢复失败: {str(e)}'
            }

    def _restore_from_file(self, backup_path: str, create_backup: bool = True) -> Dict:
        """从文件备份恢复 - 传统方式"""
        try:
            logger.info(f"📄 开始传统文件恢复: {backup_path}")

            # 获取恢复前状态
            before_cookies = []
            if os.path.exists(self.cookies_file):
                before_cookies = self._analyze_cookies()

            # 创建当前状态备份
            current_backup = None
            if create_backup:
                current_backup = self._create_backup()

            # 直接复制文件
            shutil.copy(backup_path, self.cookies_file)

            # 获取恢复后状态
            after_cookies = self._analyze_cookies()

            logger.info(f"✅ 传统恢复完成!")
            logger.info(f"📊 恢复前cookies: {before_cookies}")
            logger.info(f"📊 恢复后cookies: {after_cookies}")

            return {
                'success': True,
                'message': '已从文件备份恢复',
                'method': 'file_restore',
                'current_backup': current_backup,
                'before_cookies': before_cookies,
                'after_cookies': after_cookies
            }

        except Exception as e:
            logger.error(f"❌ 文件恢复失败: {e}")
            return {
                'success': False,
                'error': f'文件恢复失败: {str(e)}'
            }

    def inspect_backup(self, backup_filename: str) -> Dict:
        """检查备份文件内容"""
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 安全检查：确保文件名符合备份文件格式
            if not backup_filename.startswith('youtube_cookies_backup_'):
                return {
                    'success': False,
                    'error': '无效的备份文件名'
                }

            # 读取并分析备份文件
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 分析内容
            lines = content.split('\n')
            valid_lines = [line for line in lines if line.strip() and not line.startswith('#')]

            # 分析重要cookies（临时修改cookies_file路径来分析备份）
            original_cookies_file = self.cookies_file
            self.cookies_file = backup_path
            important_cookies = self._analyze_cookies()
            self.cookies_file = original_cookies_file  # 恢复原路径

            # 分析cookies过期时间
            expiration_analysis = self._analyze_cookies_expiration(backup_path)

            # 获取文件信息
            stat = os.stat(backup_path)
            file_size = stat.st_size
            modified_time = stat.st_mtime
            modified_date = datetime.fromtimestamp(modified_time)

            return {
                'success': True,
                'filename': backup_filename,
                'file_size': file_size,
                'content_length': len(content),
                'valid_lines': len(valid_lines),
                'important_cookies': important_cookies,
                'important_cookies_count': len(important_cookies),
                'important_cookies_total': len(self._get_important_cookies_list()),
                'modified_time': modified_date.strftime('%Y-%m-%d %H:%M:%S'),
                'is_complete': len(important_cookies) >= 3,
                'analysis': {
                    'has_sid': 'SID' in important_cookies,
                    'has_hsid': 'HSID' in important_cookies,
                    'has_ssid': 'SSID' in important_cookies,
                    'has_apisid': 'APISID' in important_cookies,
                    'has_sapisid': 'SAPISID' in important_cookies,
                    'has_login_info': 'LOGIN_INFO' in important_cookies,
                    'has_visitor_info': 'VISITOR_INFO1_LIVE' in important_cookies
                },
                'expiration_analysis': expiration_analysis
            }

        except Exception as e:
            logger.error(f"检查备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_backup(self, backup_filename: str) -> Dict:
        """删除备份文件"""
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 安全检查：确保文件名符合备份文件格式
            if not backup_filename.startswith('youtube_cookies_backup_'):
                return {
                    'success': False,
                    'error': '无效的备份文件名'
                }

            # 删除备份文件
            os.remove(backup_path)

            logger.info(f"✅ 已删除备份文件: {backup_filename}")

            return {
                'success': True,
                'message': f'备份文件已删除: {backup_filename}'
            }

        except Exception as e:
            logger.error(f"删除备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }



# 全局实例
_cookies_manager = CookiesManager()

def get_cookies_manager():
    """获取cookies管理器实例"""
    return _cookies_manager
