"""
YouTube Cookies管理器
负责cookies的导入、验证、备份和管理
"""

import os
import json
import logging
import subprocess
import shutil
import glob
from datetime import datetime
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

class CookiesManager:
    """YouTube Cookies管理器"""
    
    def __init__(self):
        # 使用绝对路径，确保在容器中正确工作
        self.config_dir = os.path.abspath('webapp/config')
        self.backup_pattern = '*_cookies_backup_*.txt'

        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)

        # 平台配置：定义各平台的域名和重要cookies
        self.platform_configs = {
            'youtube': {
                'domains': ['youtube.com', 'google.com', '.youtube.com', '.google.com'],
                'important_cookies': [
                    'SID', 'HSID', 'SSID', 'APISID', 'SAPISID', 'LOGIN_INFO', 'VISITOR_INFO1_LIVE',
                    '__Secure-1PSID', '__Secure-3PSID', '__Secure-1PAPISID', '__Secure-3PAPISID',
                    '__Secure-1PSIDCC', '__Secure-3PSIDCC', '__Secure-1PSIDTS', '__Secure-3PSIDTS',
                    'SIDCC', 'CONSENT', 'NID', 'AEC'
                ],
                'auth_cookies': ['SID', '__Secure-1PSID', '__Secure-3PSID']
            },
            'twitter': {
                'domains': ['twitter.com', 'x.com', '.twitter.com', '.x.com'],
                'important_cookies': [
                    'auth_token', 'ct0', 'guest_id', 'personalization_id', 'gt', 'twid',
                    '_twitter_sess', 'remember_checked_on', 'kdt', 'dnt', 'mbox'
                ],
                'auth_cookies': ['auth_token', 'ct0']
            },
            'instagram': {
                'domains': ['instagram.com', '.instagram.com'],
                'important_cookies': [
                    'sessionid', 'csrftoken', 'ds_user_id', 'ig_did', 'ig_nrcb'
                ],
                'auth_cookies': ['sessionid']
            },
            'tiktok': {
                'domains': ['tiktok.com', '.tiktok.com'],
                'important_cookies': [
                    'sessionid', 'sid_tt', 'uid_tt', 'sid_guard', 'uid_guard', 'ssid_ucp_v1'
                ],
                'auth_cookies': ['sessionid', 'sid_tt']
            },
            'bilibili': {
                'domains': ['bilibili.com', '.bilibili.com'],
                'important_cookies': [
                    'SESSDATA', 'bili_jct', 'DedeUserID', 'DedeUserID__ckMd5', 'sid'
                ],
                'auth_cookies': ['SESSDATA']
            }
        }

    def get_platform_cookies_file(self, platform: str) -> str:
        """获取指定平台的cookies文件路径"""
        return os.path.join(self.config_dir, f'{platform}_cookies.txt')

    def get_all_platform_cookies_files(self) -> Dict[str, str]:
        """获取所有平台的cookies文件路径"""
        return {platform: self.get_platform_cookies_file(platform)
                for platform in self.platform_configs.keys()}

    def detect_platform_from_url(self, url: str) -> str:
        """从URL智能检测平台"""
        url_lower = url.lower()

        for platform, config in self.platform_configs.items():
            for domain in config['domains']:
                # 只移除前导点，保留域名中的点
                clean_domain = domain.lstrip('.')
                if clean_domain in url_lower:
                    return platform

        return 'unknown'

    def detect_platform_from_domain(self, domain: str) -> str:
        """从域名检测平台"""
        for platform, config in self.platform_configs.items():
            for platform_domain in config['domains']:
                if platform_domain in domain:
                    return platform
        return 'unknown'

    def get_all_important_cookies(self) -> List[str]:
        """获取所有平台的重要cookies列表"""
        all_cookies = []
        for platform, config in self.platform_configs.items():
            all_cookies.extend(config['important_cookies'])
        return list(set(all_cookies))  # 去重

    def get_platform_cookies(self, platform: str) -> List[str]:
        """获取指定平台的重要cookies"""
        if platform in self.platform_configs:
            return self.platform_configs[platform]['important_cookies']
        return []

    def analyze_cookies_by_platform(self) -> Dict:
        """按平台分析cookies - 分析各平台独立文件"""
        platform_analysis = {}

        # 初始化平台分析结构
        for platform, config in self.platform_configs.items():
            platform_analysis[platform] = {
                'found_cookies': [],
                'important_cookies': config['important_cookies'],
                'auth_cookies': config['auth_cookies'],
                'completeness': 0.0,
                'has_auth': False,
                'file_exists': False
            }

        # 分析每个平台的cookies文件
        platform_files = self.get_all_platform_cookies_files()
        for platform, file_path in platform_files.items():
            if os.path.exists(file_path):
                platform_analysis[platform]['file_exists'] = True

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if line.strip() and not line.startswith('#'):
                                parts = line.strip().split('\t')
                                if len(parts) >= 6:
                                    cookie_name = parts[5]

                                    # 检查是否是重要cookie
                                    important_cookies = platform_analysis[platform]['important_cookies']
                                    if cookie_name in important_cookies:
                                        platform_analysis[platform]['found_cookies'].append(cookie_name)

                except Exception as e:
                    logger.error(f"分析 {platform} 平台cookies失败: {e}")

        # 计算每个平台的完整性和认证状态
        for platform, data in platform_analysis.items():
            data['found_cookies'] = list(set(data['found_cookies']))  # 去重
            data['completeness'] = len(data['found_cookies']) / len(data['important_cookies']) if data['important_cookies'] else 0
            data['has_auth'] = any(cookie in data['found_cookies'] for cookie in data['auth_cookies'])

        return platform_analysis

    def get_cookies_for_url(self, url: str) -> str:
        """根据URL自动调取对应平台的cookies文件 - 核心功能"""
        # 1. 检测平台
        platform = self.detect_platform_from_url(url)

        # 2. 获取对应平台cookies文件
        if platform == 'unknown':
            platform = 'youtube'  # 默认使用YouTube

        cookies_file = self.get_platform_cookies_file(platform)

        # 3. 检查文件是否存在
        if os.path.exists(cookies_file):
            logger.info(f"🍪 使用 {platform} 平台cookies: {cookies_file}")
            return cookies_file
        else:
            # 4. 备用：找任何可用的cookies文件
            for p, file_path in self.get_all_platform_cookies_files().items():
                if os.path.exists(file_path):
                    logger.info(f"🔄 {platform} 平台文件不存在，使用 {p} 平台cookies: {file_path}")
                    return file_path

            logger.error(f"❌ 没有找到任何cookies文件")
            return cookies_file

    def get_status(self) -> Dict:
        """获取多平台cookies状态"""
        try:
            platform_files = self.get_all_platform_cookies_files()
            existing_files = {}
            total_size = 0
            latest_modified = None

            # 检查每个平台的cookies文件
            for platform, file_path in platform_files.items():
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    file_size = stat.st_size
                    modified_time = stat.st_mtime
                    modified_date = datetime.fromtimestamp(modified_time)

                    existing_files[platform] = {
                        'file_path': file_path,
                        'file_size': file_size,
                        'modified_time': modified_time,
                        'modified_date': modified_date.isoformat(),
                        'days_since_modified': (datetime.now() - modified_date).days
                    }

                    total_size += file_size
                    if latest_modified is None or modified_time > latest_modified:
                        latest_modified = modified_time

            if not existing_files:
                return {
                    'exists': False,
                    'status': 'missing',
                    'message': '未找到任何平台的cookies文件'
                }

            # 分析所有平台的cookies
            platform_analysis = self.analyze_cookies_by_platform()

            # 计算总体状态
            latest_modified_date = datetime.fromtimestamp(latest_modified)
            days_since_modified = (datetime.now() - latest_modified_date).days

            # 检查认证状态
            auth_platforms = [p for p, data in platform_analysis.items() if data.get('has_auth', False)]

            if days_since_modified > 300:  # 10个月
                status = 'expired'
                message = f'Cookies可能已过期，建议更新 (最后更新: {days_since_modified}天前)'
            elif days_since_modified > 180:  # 6个月
                status = 'warning'
                message = f'Cookies需要关注，建议近期更新 (最后更新: {days_since_modified}天前)'
            elif len(auth_platforms) == 0:
                status = 'incomplete'
                message = '所有平台都缺少认证cookies'
            else:
                status = 'good'
                message = f'找到 {len(existing_files)} 个平台的cookies，{len(auth_platforms)} 个平台有认证'

            return {
                'exists': True,
                'status': status,
                'message': message,
                'total_platforms': len(existing_files),
                'platform_files': existing_files,
                'total_size': total_size,
                'latest_modified': latest_modified,
                'latest_modified_date': latest_modified_date.isoformat(),
                'days_since_modified': days_since_modified,
                'platform_analysis': platform_analysis,
                'supported_platforms': list(existing_files.keys()),
                'auth_platforms': auth_platforms
            }

        except Exception as e:
            logger.error(f"获取cookies状态失败: {e}")
            return {
                'exists': False,
                'status': 'error',
                'message': f'检查状态失败: {str(e)}'
            }

    def delete_cookies(self) -> Dict:
        """删除所有平台的cookies文件"""
        try:
            platform_files = self.get_all_platform_cookies_files()
            deleted_files = []

            for platform, file_path in platform_files.items():
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_files.append(f"{platform}_cookies.txt")
                    logger.info(f"🗑️ 已删除 {platform} 平台cookies文件")

            if deleted_files:
                return {
                    'success': True,
                    'message': f'已删除 {len(deleted_files)} 个平台的cookies文件',
                    'deleted_files': deleted_files
                }
            else:
                return {
                    'success': True,
                    'message': '没有找到需要删除的cookies文件'
                }

        except Exception as e:
            logger.error(f"删除cookies失败: {e}")
            return {
                'success': False,
                'error': f'删除失败: {str(e)}'
            }

    def _get_important_cookies_list(self) -> List[str]:
        """获取重要cookies列表定义"""
        return self.get_all_important_cookies()

    def _analyze_cookies(self) -> List[str]:
        """分析多平台cookies文件，返回找到的重要cookies"""
        important_cookies = self._get_important_cookies_list()
        found_cookies = []

        try:
            platform_files = self.get_all_platform_cookies_files()
            for platform, file_path in platform_files.items():
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if line.strip() and not line.startswith('#'):
                                parts = line.strip().split('\t')
                                if len(parts) >= 6:
                                    cookie_name = parts[5]

                                    # 检查是否是重要cookie
                                    if cookie_name in important_cookies:
                                        found_cookies.append(cookie_name)
                                        logger.debug(f"🔍 找到重要cookie: {platform} -> {cookie_name}")

        except Exception as e:
            logger.error(f"分析cookies失败: {e}")

        # 移除重复项并保持顺序
        found_cookies = list(dict.fromkeys(found_cookies))
        return found_cookies

    def _analyze_cookies_expiration(self, cookies_file_path: str) -> Dict:
        """分析cookies的过期时间"""
        try:
            current_time = datetime.now()
            current_timestamp = int(current_time.timestamp())

            cookies_expiration = {}
            earliest_expiry = None
            latest_expiry = None
            expired_count = 0
            valid_count = 0

            with open(cookies_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 7:
                            domain = parts[0]
                            cookie_name = parts[5]

                            # 检查是否是重要cookie
                            important_cookies = self._get_important_cookies_list()

                            platform = self.detect_platform_from_domain(domain)
                            if platform != 'unknown' and cookie_name in important_cookies:
                                try:
                                    expiry_timestamp = int(parts[4])
                                    expiry_date = datetime.fromtimestamp(expiry_timestamp)

                                    # 计算剩余时间
                                    remaining_seconds = expiry_timestamp - current_timestamp
                                    remaining_days = remaining_seconds / (24 * 3600)

                                    is_expired = remaining_seconds <= 0
                                    if is_expired:
                                        expired_count += 1
                                    else:
                                        valid_count += 1

                                    cookies_expiration[cookie_name] = {
                                        'expiry_timestamp': expiry_timestamp,
                                        'expiry_date': expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
                                        'remaining_days': max(0, remaining_days),
                                        'remaining_seconds': max(0, remaining_seconds),
                                        'is_expired': is_expired,
                                        'domain': domain,
                                        'line_number': line_num
                                    }

                                    # 跟踪最早和最晚过期时间
                                    if earliest_expiry is None or expiry_timestamp < earliest_expiry:
                                        earliest_expiry = expiry_timestamp
                                    if latest_expiry is None or expiry_timestamp > latest_expiry:
                                        latest_expiry = expiry_timestamp

                                except (ValueError, IndexError):
                                    # 过期时间格式错误
                                    cookies_expiration[cookie_name] = {
                                        'expiry_timestamp': None,
                                        'expiry_date': '格式错误',
                                        'remaining_days': 0,
                                        'remaining_seconds': 0,
                                        'is_expired': True,
                                        'domain': domain,
                                        'line_number': line_num
                                    }
                                    expired_count += 1

            # 计算整体统计
            total_cookies = len(cookies_expiration)
            if total_cookies == 0:
                return {
                    'total_cookies': 0,
                    'valid_cookies': 0,
                    'expired_cookies': 0,
                    'cookies_details': {},
                    'overall_status': 'no_cookies',
                    'earliest_expiry': None,
                    'latest_expiry': None,
                    'min_remaining_days': 0,
                    'max_remaining_days': 0
                }

            # 计算剩余时间范围
            valid_cookies_details = {k: v for k, v in cookies_expiration.items() if not v['is_expired']}
            if valid_cookies_details:
                min_remaining_days = min(v['remaining_days'] for v in valid_cookies_details.values())
                max_remaining_days = max(v['remaining_days'] for v in valid_cookies_details.values())
            else:
                min_remaining_days = 0
                max_remaining_days = 0

            # 判断整体状态
            if expired_count == total_cookies:
                overall_status = 'all_expired'
            elif expired_count > total_cookies * 0.5:
                overall_status = 'mostly_expired'
            elif min_remaining_days < 30:
                overall_status = 'expiring_soon'
            elif min_remaining_days < 90:
                overall_status = 'good'
            else:
                overall_status = 'excellent'

            return {
                'total_cookies': total_cookies,
                'valid_cookies': valid_count,
                'expired_cookies': expired_count,
                'cookies_details': cookies_expiration,
                'overall_status': overall_status,
                'earliest_expiry': datetime.fromtimestamp(earliest_expiry).strftime('%Y-%m-%d %H:%M:%S') if earliest_expiry else None,
                'latest_expiry': datetime.fromtimestamp(latest_expiry).strftime('%Y-%m-%d %H:%M:%S') if latest_expiry else None,
                'min_remaining_days': min_remaining_days,
                'max_remaining_days': max_remaining_days
            }

        except Exception as e:
            logger.error(f"分析cookies过期时间失败: {e}")
            return {
                'total_cookies': 0,
                'valid_cookies': 0,
                'expired_cookies': 0,
                'cookies_details': {},
                'overall_status': 'error',
                'error': str(e)
            }

    def _compare_cookies_before_after(self, before_cookies: List[str], after_cookies: List[str], backup_cookies: List[str]) -> Dict:
        """对比恢复前后的cookies变化"""
        before_set = set(before_cookies)
        after_set = set(after_cookies)
        backup_set = set(backup_cookies)

        added = list(after_set - before_set)  # 新增的cookies
        lost = list(before_set - after_set)   # 丢失的cookies
        kept = list(before_set & after_set)   # 保持的cookies

        # 检查是否按预期恢复
        expected_from_backup = list(backup_set)
        actually_restored = list(after_set)

        return {
            'added': added,
            'lost': lost,
            'kept': kept,
            'expected_from_backup': expected_from_backup,
            'actually_restored': actually_restored,
            'restoration_success': backup_set == after_set,
            'improvement': len(after_cookies) > len(before_cookies)
        }

    def test_cookies(self) -> Dict:
        """测试多平台cookies有效性"""
        try:
            platform_files = self.get_all_platform_cookies_files()
            existing_files = {p: f for p, f in platform_files.items() if os.path.exists(f)}

            if not existing_files:
                return {
                    'success': False,
                    'valid': False,
                    'error': '未找到任何平台的cookies文件'
                }

            # 分析所有平台的cookies
            platform_analysis = self.analyze_cookies_by_platform()

            # 选择一个有认证的平台进行测试
            test_platform = None
            test_file = None

            # 优先选择YouTube进行测试
            if 'youtube' in existing_files and platform_analysis.get('youtube', {}).get('has_auth', False):
                test_platform = 'youtube'
                test_file = existing_files['youtube']
            else:
                # 选择第一个有认证的平台
                for platform, file_path in existing_files.items():
                    if platform_analysis.get(platform, {}).get('has_auth', False):
                        test_platform = platform
                        test_file = file_path
                        break

            if not test_platform:
                return {
                    'success': True,
                    'valid': False,
                    'message': '所有平台都缺少认证cookies',
                    'error': '请确保cookies包含必要的认证字段',
                    'platform_analysis': platform_analysis
                }

            # 检查cookies文件内容
            logger.info(f"🔍 检查 {test_platform} 平台cookies文件: {test_file}")
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    logger.info(f"📄 {test_platform} cookies文件大小: {len(content)} 字符")

                    # 分析cookies内容
                    lines = content.split('\n')
                    valid_lines = [line for line in lines if line.strip() and not line.startswith('#')]
                    logger.info(f"📊 {test_platform} 有效cookies行数: {len(valid_lines)}")

            except Exception as e:
                logger.error(f"读取 {test_platform} cookies文件失败: {e}")
                return {
                    'success': False,
                    'valid': False,
                    'error': f'无法读取 {test_platform} cookies文件: {str(e)}'
                }

            # 根据平台选择测试URL
            test_urls = {
                'youtube': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'twitter': 'https://x.com/elonmusk',
                'instagram': 'https://www.instagram.com/',
                'tiktok': 'https://www.tiktok.com/',
                'bilibili': 'https://www.bilibili.com/'
            }

            test_url = test_urls.get(test_platform, test_urls['youtube'])

            # 使用yt-dlp测试
            cmd = [
                'yt-dlp', '--cookies', test_file,
                '--dump-json', '--no-warnings', '--no-check-certificate',
                test_url
            ]

            logger.info(f"🧪 测试 {test_platform} 平台cookies: {test_url}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            logger.info(f"🧪 {test_platform} 测试结果返回码: {result.returncode}")
            if result.stdout:
                logger.info(f"🧪 测试标准输出: {result.stdout[:300]}...")
            if result.stderr:
                logger.info(f"🧪 测试错误输出: {result.stderr[:500]}...")

            if result.returncode == 0:
                # 检查是否成功获取到视频信息
                try:
                    import json
                    video_info = json.loads(result.stdout)
                    if video_info.get('title'):
                        logger.info(f"✅ 成功获取视频信息: {video_info.get('title', 'Unknown')}")
                        return {
                            'success': True,
                            'valid': True,
                            'message': f'{test_platform} 平台cookies有效，成功获取视频: {video_info.get("title", "Unknown")}',
                            'test_platform': test_platform
                        }
                except:
                    pass

                return {
                    'success': True,
                    'valid': True,
                    'message': f'{test_platform} 平台cookies有效，可以正常下载',
                    'test_platform': test_platform
                }
            else:
                # 详细分析错误信息
                error_msg = result.stderr

                if 'Sign in to confirm you\'re not a bot' in error_msg:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies可能已过期，遇到bot检测',
                        'error': 'YouTube要求重新认证，请更新cookies'
                    }
                elif 'Private video' in error_msg or 'Video unavailable' in error_msg:
                    # 如果是视频不可用，尝试另一个测试URL
                    logger.info(f"🔄 {test_platform} 平台测试视频不可用，尝试另一个URL...")
                    return self._test_with_alternative_url(test_platform, test_file)
                elif 'HTTP Error 403' in error_msg:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies可能已过期或无效',
                        'error': 'YouTube返回403错误，请更新cookies'
                    }
                else:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Cookies测试失败',
                        'error': error_msg[:300] if error_msg else '未知错误'
                    }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'valid': False,
                'error': '测试超时（60秒）'
            }
        except Exception as e:
            logger.error(f"测试cookies失败: {e}")
            return {
                'success': False,
                'valid': False,
                'error': str(e)
            }

    def _test_with_alternative_url(self, test_platform: str = 'youtube', test_file: str = None) -> Dict:
        """使用备用URL测试cookies"""
        try:
            # 根据平台选择备用测试URL
            alternative_urls = {
                'youtube': 'https://www.youtube.com/watch?v=jNQXAC9IVRw',
                'twitter': 'https://x.com/twitter',
                'instagram': 'https://www.instagram.com/instagram/',
                'tiktok': 'https://www.tiktok.com/@tiktok',
                'bilibili': 'https://www.bilibili.com/video/BV1xx411c7mD'
            }

            test_url = alternative_urls.get(test_platform, alternative_urls['youtube'])

            if not test_file:
                test_file = self.get_platform_cookies_file(test_platform)

            cmd = [
                'yt-dlp', '--cookies', test_file,
                '--dump-json', '--no-warnings', '--no-check-certificate',
                test_url
            ]

            logger.info(f"🔄 {test_platform} 平台备用测试: {test_url}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=45)

            if result.returncode == 0:
                return {
                    'success': True,
                    'valid': True,
                    'message': f'{test_platform} 平台cookies有效（备用测试通过）',
                    'test_platform': test_platform
                }
            else:
                return {
                    'success': True,
                    'valid': False,
                    'message': f'{test_platform} 平台cookies可能无效',
                    'error': f'备用测试也失败: {result.stderr[:200] if result.stderr else "未知错误"}',
                    'test_platform': test_platform
                }

        except Exception as e:
            logger.error(f"{test_platform} 平台备用测试失败: {e}")
            return {
                'success': True,
                'valid': False,
                'message': f'{test_platform} 平台cookies测试失败',
                'error': f'备用测试异常: {str(e)}',
                'test_platform': test_platform
            }
    
    def import_cookies(self, cookies_content: str, format_type: str = 'auto', full_backup: bool = False) -> Dict:
        """导入cookies - 智能分平台保存"""
        try:
            if not cookies_content.strip():
                return {
                    'success': False,
                    'error': '请提供cookies内容'
                }

            # 备份现有cookies（包括原始格式）
            backup_file = self._create_backup_with_original(cookies_content, format_type)

            # 检测和转换格式
            final_content, detected_format = self._convert_cookies_format(cookies_content, format_type, full_backup)

            # 验证cookies内容
            validation_result = self._validate_cookies_content(final_content)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error'],
                    'found_cookies': validation_result.get('found_cookies', [])
                }

            # 按平台分离cookies并保存到独立文件
            platform_files = self._save_cookies_by_platform(final_content)

            logger.info(f"✅ Cookies已按平台保存到 {len(platform_files)} 个文件")

            # 测试主要平台的cookies
            test_results = {}
            for platform, file_path in platform_files.items():
                if platform == 'youtube':  # 优先测试YouTube
                    test_results[platform] = self._test_platform_cookies(platform, file_path)

            return {
                'success': True,
                'message': f'Cookies导入成功 ({"完整备份" if full_backup else "智能提取"}模式)',
                'detected_format': detected_format,
                'found_cookies': validation_result['found_cookies'],
                'backup_file': backup_file,
                'platform_files': platform_files,
                'test_results': test_results,
                'import_mode': 'full_backup' if full_backup else 'smart_extract'
            }

        except Exception as e:
            logger.error(f"导入cookies失败: {e}")
            return {
                'success': False,
                'error': f'导入失败: {str(e)}'
            }

    def _save_cookies_by_platform(self, cookies_content: str) -> Dict[str, str]:
        """按平台分离cookies并保存到独立文件"""
        platform_cookies = {}

        # 初始化平台cookies字典
        for platform in self.platform_configs.keys():
            platform_cookies[platform] = []

        # 分析cookies内容，按平台分类
        for line in cookies_content.split('\n'):
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split('\t')
                if len(parts) >= 6:
                    domain = parts[0]

                    # 检测这个cookie属于哪个平台
                    platform = self.detect_platform_from_domain(domain)
                    if platform != 'unknown':
                        platform_cookies[platform].append(line)

        # 为每个有cookies的平台生成文件
        platform_files = {}
        for platform, cookies_lines in platform_cookies.items():
            if cookies_lines:  # 只为有cookies的平台生成文件
                file_path = self.get_platform_cookies_file(platform)

                # 生成文件内容
                file_content = "# Netscape HTTP Cookie File\n"
                file_content += f"# Auto-generated {platform} cookies for yt-dlp\n"
                file_content += f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                file_content += '\n'.join(cookies_lines)

                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(file_content)

                platform_files[platform] = file_path
                logger.info(f"📁 {platform} 平台: 保存 {len(cookies_lines)} 个cookies到 {file_path}")

        return platform_files

    def _test_platform_cookies(self, platform: str, cookies_file: str) -> Dict:
        """测试指定平台的cookies"""
        try:
            # 根据平台选择测试URL
            test_urls = {
                'youtube': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'twitter': 'https://x.com/elonmusk',  # 简单的公开推文
                'instagram': 'https://www.instagram.com/',
                'tiktok': 'https://www.tiktok.com/',
                'bilibili': 'https://www.bilibili.com/'
            }

            test_url = test_urls.get(platform, test_urls['youtube'])

            cmd = [
                'yt-dlp', '--cookies', cookies_file,
                '--dump-json', '--no-warnings', '--no-check-certificate',
                test_url
            ]

            logger.info(f"🧪 测试 {platform} 平台cookies: {test_url}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return {
                    'success': True,
                    'valid': True,
                    'message': f'{platform} 平台cookies有效'
                }
            else:
                return {
                    'success': True,
                    'valid': False,
                    'message': f'{platform} 平台cookies可能无效',
                    'error': result.stderr[:200] if result.stderr else '未知错误'
                }

        except Exception as e:
            logger.error(f"测试 {platform} 平台cookies失败: {e}")
            return {
                'success': False,
                'valid': False,
                'error': str(e)
            }

    def _create_backup(self) -> Optional[str]:
        """创建多平台cookies备份"""
        try:
            platform_files = self.get_all_platform_cookies_files()
            existing_files = {p: f for p, f in platform_files.items() if os.path.exists(f)}

            if not existing_files:
                logger.info("没有现有cookies文件需要备份")
                return None

            timestamp = int(datetime.now().timestamp())
            backup_files = []

            # 为每个平台创建备份
            for platform, file_path in existing_files.items():
                backup_file = f'{platform}_cookies_backup_{timestamp}.txt'
                backup_path = os.path.join(self.config_dir, backup_file)
                shutil.copy(file_path, backup_path)
                backup_files.append(backup_file)
                logger.info(f"已备份 {platform} cookies到: {backup_file}")

            # 返回主要备份文件名（YouTube优先，否则第一个）
            if 'youtube' in existing_files:
                return f'youtube_cookies_backup_{timestamp}.txt'
            else:
                return backup_files[0] if backup_files else None

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
        return None

    def _create_backup_with_original(self, original_content: str, format_type: str) -> Optional[str]:
        """创建包含原始格式的备份"""
        try:
            # 先备份当前的cookies文件（如果存在）
            current_backup = self._create_backup()

            # 创建原始格式的备份
            timestamp = int(datetime.now().timestamp())
            original_backup_file = f'multi_platform_cookies_original_{timestamp}.json'
            original_backup_path = os.path.join(self.config_dir, original_backup_file)

            # 保存原始内容和元数据
            backup_data = {
                'timestamp': timestamp,
                'format_type': format_type,
                'original_content': original_content,
                'created_time': datetime.now().isoformat(),
                'note': 'Original format backup for easy restoration'
            }

            with open(original_backup_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"已创建原始格式备份: {original_backup_file}")
            return current_backup

        except Exception as e:
            logger.error(f"创建原始格式备份失败: {e}")
            return self._create_backup()  # 降级到普通备份
    
    def _convert_cookies_format(self, content: str, format_type: str, full_backup: bool = False) -> Tuple[str, str]:
        """转换cookies格式 - 支持完整备份或智能提取"""
        # 检测格式
        detected_format = 'netscape'
        try:
            json.loads(content)
            detected_format = 'json'
        except:
            if content.startswith('# Netscape HTTP Cookie File'):
                detected_format = 'netscape'
            elif '\t' in content and ('youtube.com' in content or 'google.com' in content):
                detected_format = 'netscape'

        if detected_format == 'json':
            # JSON格式转换为Netscape格式
            cookies_data = json.loads(content)
            netscape_lines = [
                '# Netscape HTTP Cookie File',
                '# This is a generated file! Do not edit.',
                f'# {"Complete backup" if full_backup else "Auto-extracted Google/YouTube cookies"} for yt-dlp',
                ''
            ]

            extracted_count = 0

            if full_backup:
                # 完整备份模式：转换所有cookies
                logger.info("🔄 完整备份模式：转换所有cookies")
                for cookie in cookies_data:
                    if isinstance(cookie, dict) and 'domain' in cookie:
                        domain = cookie.get('domain', '')
                        name = cookie.get('name', '')
                        domain_flag = 'TRUE' if domain.startswith('.') else 'FALSE'
                        path = cookie.get('path', '/')
                        secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'

                        # 处理过期时间
                        expiration = cookie.get('expirationDate', 0)
                        if isinstance(expiration, float):
                            expiration = int(expiration)
                        elif expiration == 0:
                            expiration = 2147483647  # 默认远期时间

                        value = cookie.get('value', '')

                        # 构建Netscape格式行
                        line = f'{domain}\t{domain_flag}\t{path}\t{secure}\t{expiration}\t{name}\t{value}'
                        netscape_lines.append(line)
                        extracted_count += 1

                logger.info(f"🍪 完整备份：转换了 {extracted_count} 个cookies")
            else:
                # 智能提取模式：提取所有支持平台的cookies
                all_domains = []
                for platform, config in self.platform_configs.items():
                    all_domains.extend(config['domains'])
                important_cookies = self._get_important_cookies_list()

                for cookie in cookies_data:
                    if isinstance(cookie, dict) and 'domain' in cookie:
                        domain = cookie.get('domain', '')
                        name = cookie.get('name', '')

                        # 检查是否是目标域名
                        is_target_domain = any(target_domain in domain for target_domain in all_domains)

                        # 检查是否是重要cookie或者是支持的平台域名
                        platform = self.detect_platform_from_domain(domain)
                        if platform != 'unknown' and (name in important_cookies or is_target_domain):
                            domain_flag = 'TRUE' if domain.startswith('.') else 'FALSE'
                            path = cookie.get('path', '/')
                            secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'

                            # 处理过期时间
                            expiration = cookie.get('expirationDate', 0)
                            if isinstance(expiration, float):
                                expiration = int(expiration)
                            elif expiration == 0:
                                expiration = 2147483647  # 默认远期时间

                            value = cookie.get('value', '')

                            # 构建Netscape格式行
                            line = f'{domain}\t{domain_flag}\t{path}\t{secure}\t{expiration}\t{name}\t{value}'
                            netscape_lines.append(line)
                            extracted_count += 1

                            logger.info(f"✅ 提取cookie: {domain} -> {name}")

                logger.info(f"🍪 智能提取：从JSON中提取了 {extracted_count} 个Google/YouTube cookies")

                if extracted_count == 0:
                    logger.warning("⚠️ 未找到任何Google/YouTube相关cookies，切换到完整备份模式")
                    # 如果没有找到目标cookies，自动切换到完整备份
                    return self._convert_cookies_format(content, format_type, full_backup=True)

            return '\n'.join(netscape_lines), detected_format
        else:
            # 已经是Netscape格式
            return content, detected_format
    
    def _validate_cookies_content(self, content: str) -> Dict:
        """验证cookies内容 - 支持多平台域名"""
        important_cookies = self._get_important_cookies_list()

        found_cookies = []
        platform_cookies = {}

        for line in content.split('\n'):
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split('\t')
                if len(parts) >= 7:
                    domain = parts[0]
                    cookie_name = parts[5]

                    # 检测平台
                    platform = self.detect_platform_from_domain(domain)
                    if platform != 'unknown' and cookie_name in important_cookies:
                        found_cookies.append(cookie_name)

                        if platform not in platform_cookies:
                            platform_cookies[platform] = []
                        platform_cookies[platform].append(cookie_name)

        # 移除重复项
        found_cookies = list(set(found_cookies))

        # 验证逻辑：检查是否有任何平台的认证cookies
        has_auth = False
        for platform, config in self.platform_configs.items():
            if platform in platform_cookies:
                platform_found = platform_cookies[platform]
                auth_cookies = config['auth_cookies']
                if any(cookie in platform_found for cookie in auth_cookies):
                    has_auth = True
                    break

        if len(found_cookies) < 1 and not has_auth:
            return {
                'valid': False,
                'error': f'cookies内容不完整，只找到 {len(found_cookies)} 个重要cookies',
                'found_cookies': found_cookies,
                'platform_cookies': platform_cookies
            }

        return {
            'valid': True,
            'found_cookies': found_cookies,
            'platform_cookies': platform_cookies
        }
    
    def list_backups(self) -> List[Dict]:
        """列出备份文件"""
        try:
            backup_pattern = os.path.join(self.config_dir, self.backup_pattern)
            backup_files = glob.glob(backup_pattern)

            # 注意：原始格式备份文件会在检查备份时自动关联

            backups = []

            # 处理普通备份
            for file_path in backup_files:
                filename = os.path.basename(file_path)
                timestamp_str = filename.replace('youtube_cookies_backup_', '').replace('.txt', '')
                try:
                    timestamp = int(timestamp_str)
                    created_time = datetime.fromtimestamp(timestamp)
                    file_size = os.path.getsize(file_path)

                    # 检查是否有对应的原始格式备份
                    original_file = f'youtube_cookies_original_{timestamp}.json'
                    original_path = os.path.join(self.config_dir, original_file)
                    has_original = os.path.exists(original_path)

                    backups.append({
                        'filename': filename,
                        'path': file_path,
                        'created_time': created_time.isoformat(),
                        'created_time_str': created_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'file_size': file_size,
                        'has_original': has_original,
                        'original_file': original_file if has_original else None,
                        'backup_type': 'smart' if has_original else 'legacy'
                    })
                except ValueError:
                    continue

            # 按时间倒序排列
            backups.sort(key=lambda x: x['created_time'], reverse=True)
            return backups

        except Exception as e:
            logger.error(f"列出备份失败: {e}")
            return []
    
    def restore_backup(self, backup_filename: str, create_backup: bool = True) -> Dict:
        """恢复备份 - 智能恢复，优先使用原始格式

        Args:
            backup_filename: 要恢复的备份文件名
            create_backup: 是否在恢复前创建当前cookies的备份
        """
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            logger.info(f"🔄 开始智能恢复备份: {backup_filename}")
            logger.info(f"📁 备份文件路径: {backup_path}")
            logger.info(f"🎯 目标cookies文件: {self.cookies_file}")

            if not os.path.exists(backup_path):
                logger.error(f"❌ 备份文件不存在: {backup_path}")
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 检查是否有对应的原始格式备份
            timestamp_str = backup_filename.replace('youtube_cookies_backup_', '').replace('.txt', '')
            original_file = f'youtube_cookies_original_{timestamp_str}.json'
            original_path = os.path.join(self.config_dir, original_file)

            if os.path.exists(original_path):
                logger.info(f"🎯 发现原始格式备份，使用智能恢复: {original_file}")
                return self._restore_from_original(original_path, create_backup)
            else:
                logger.info(f"📄 使用传统文件恢复方式")
                return self._restore_from_file(backup_path, create_backup)

        except Exception as e:
            logger.error(f"❌ 恢复备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _restore_from_original(self, original_path: str, create_backup: bool = True) -> Dict:
        """从原始格式备份恢复 - 模拟导入过程"""
        try:
            logger.info(f"🎯 开始从原始格式恢复: {original_path}")

            # 读取原始格式备份
            with open(original_path, 'r', encoding='utf-8') as f:
                import json
                backup_data = json.load(f)

            original_content = backup_data.get('original_content', '')
            format_type = backup_data.get('format_type', 'auto')

            if not original_content:
                return {
                    'success': False,
                    'error': '原始格式备份内容为空'
                }

            logger.info(f"📄 原始格式: {format_type}")
            logger.info(f"📊 原始内容大小: {len(original_content)} 字符")

            # 获取恢复前状态
            before_cookies = []
            if os.path.exists(self.cookies_file):
                before_cookies = self._analyze_cookies()

            # 创建当前状态备份
            current_backup = None
            if create_backup:
                current_backup = self._create_backup()

            # 模拟导入过程 - 这是关键！使用完整备份模式
            logger.info("🔄 模拟导入过程（完整备份模式）...")
            import_result = self.import_cookies(original_content, format_type, full_backup=True)

            if not import_result['success']:
                return {
                    'success': False,
                    'error': f'模拟导入失败: {import_result["error"]}'
                }

            # 获取恢复后状态
            after_cookies = self._analyze_cookies()

            logger.info(f"✅ 智能恢复成功!")
            logger.info(f"📊 恢复前cookies: {before_cookies}")
            logger.info(f"📊 恢复后cookies: {after_cookies}")

            return {
                'success': True,
                'message': f'已从原始格式智能恢复 (格式: {format_type})',
                'method': 'smart_restore',
                'original_format': format_type,
                'current_backup': current_backup,
                'before_cookies': before_cookies,
                'after_cookies': after_cookies,
                'import_result': import_result
            }

        except Exception as e:
            logger.error(f"❌ 原始格式恢复失败: {e}")
            return {
                'success': False,
                'error': f'原始格式恢复失败: {str(e)}'
            }

    def _restore_from_file(self, backup_path: str, create_backup: bool = True) -> Dict:
        """从文件备份恢复 - 传统方式"""
        try:
            logger.info(f"📄 开始传统文件恢复: {backup_path}")

            # 获取恢复前状态
            before_cookies = []
            if os.path.exists(self.cookies_file):
                before_cookies = self._analyze_cookies()

            # 创建当前状态备份
            current_backup = None
            if create_backup:
                current_backup = self._create_backup()

            # 直接复制文件
            shutil.copy(backup_path, self.cookies_file)

            # 获取恢复后状态
            after_cookies = self._analyze_cookies()

            logger.info(f"✅ 传统恢复完成!")
            logger.info(f"📊 恢复前cookies: {before_cookies}")
            logger.info(f"📊 恢复后cookies: {after_cookies}")

            return {
                'success': True,
                'message': '已从文件备份恢复',
                'method': 'file_restore',
                'current_backup': current_backup,
                'before_cookies': before_cookies,
                'after_cookies': after_cookies
            }

        except Exception as e:
            logger.error(f"❌ 文件恢复失败: {e}")
            return {
                'success': False,
                'error': f'文件恢复失败: {str(e)}'
            }

    def inspect_backup(self, backup_filename: str) -> Dict:
        """检查备份文件内容"""
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 安全检查：确保文件名符合备份文件格式
            if not backup_filename.startswith('youtube_cookies_backup_'):
                return {
                    'success': False,
                    'error': '无效的备份文件名'
                }

            # 读取并分析备份文件
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 分析内容
            lines = content.split('\n')
            valid_lines = [line for line in lines if line.strip() and not line.startswith('#')]

            # 分析重要cookies（临时修改cookies_file路径来分析备份）
            original_cookies_file = self.cookies_file
            self.cookies_file = backup_path
            important_cookies = self._analyze_cookies()
            self.cookies_file = original_cookies_file  # 恢复原路径

            # 分析cookies过期时间
            expiration_analysis = self._analyze_cookies_expiration(backup_path)

            # 获取文件信息
            stat = os.stat(backup_path)
            file_size = stat.st_size
            modified_time = stat.st_mtime
            modified_date = datetime.fromtimestamp(modified_time)

            return {
                'success': True,
                'filename': backup_filename,
                'file_size': file_size,
                'content_length': len(content),
                'valid_lines': len(valid_lines),
                'important_cookies': important_cookies,
                'important_cookies_count': len(important_cookies),
                'important_cookies_total': len(self._get_important_cookies_list()),
                'modified_time': modified_date.strftime('%Y-%m-%d %H:%M:%S'),
                'is_complete': len(important_cookies) >= 3,
                'analysis': {
                    'has_sid': 'SID' in important_cookies,
                    'has_hsid': 'HSID' in important_cookies,
                    'has_ssid': 'SSID' in important_cookies,
                    'has_apisid': 'APISID' in important_cookies,
                    'has_sapisid': 'SAPISID' in important_cookies,
                    'has_login_info': 'LOGIN_INFO' in important_cookies,
                    'has_visitor_info': 'VISITOR_INFO1_LIVE' in important_cookies
                },
                'expiration_analysis': expiration_analysis
            }

        except Exception as e:
            logger.error(f"检查备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_backup(self, backup_filename: str) -> Dict:
        """删除备份文件"""
        try:
            backup_path = os.path.join(self.config_dir, backup_filename)

            if not os.path.exists(backup_path):
                return {
                    'success': False,
                    'error': '备份文件不存在'
                }

            # 安全检查：确保文件名符合备份文件格式
            if not backup_filename.startswith('youtube_cookies_backup_'):
                return {
                    'success': False,
                    'error': '无效的备份文件名'
                }

            # 删除备份文件
            os.remove(backup_path)

            logger.info(f"✅ 已删除备份文件: {backup_filename}")

            return {
                'success': True,
                'message': f'备份文件已删除: {backup_filename}'
            }

        except Exception as e:
            logger.error(f"删除备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }



# 全局实例
_cookies_manager = CookiesManager()

def get_cookies_manager():
    """获取cookies管理器实例"""
    return _cookies_manager
