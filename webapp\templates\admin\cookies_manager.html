{% extends "base.html" %}

{% block title %}Cookies管理 - 管理员{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🍪 YouTube Cookies管理</h2>
                <div>
                    <button id="refreshStatus" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt"></i> 刷新状态
                    </button>
                    <a href="/admin" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left"></i> 返回管理
                    </a>
                </div>
            </div>

            <!-- 状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title">Cookies状态</h5>
                            <div id="cookiesStatusBadge" class="mb-2">
                                <span class="badge bg-secondary">检查中...</span>
                            </div>
                            <button id="refreshStatus" class="btn btn-sm btn-primary">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <p id="cookiesStatusText" class="card-text text-muted">正在检查状态...</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title">平台数量</h5>
                            <div id="platformCount" class="mb-2">
                                <span class="badge bg-secondary">0</span>
                            </div>
                            <button id="refreshPlatforms" class="btn btn-sm btn-success">
                                <i class="fas fa-list"></i> 查看
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要功能标签页 -->
            <ul class="nav nav-tabs" id="cookiesTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button">
                        <i class="fas fa-upload"></i> 导入Cookies
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="manage-tab" data-bs-toggle="tab" data-bs-target="#manage" type="button">
                        <i class="fas fa-cogs"></i> Cookies管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="help-tab" data-bs-toggle="tab" data-bs-target="#help" type="button">
                        <i class="fas fa-question-circle"></i> 获取指南
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="cookiesTabContent">
                <!-- 导入Cookies标签页 -->
                <div class="tab-pane fade show active" id="import" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-upload"></i> 导入新的Cookies</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 支持的格式:</h6>
                                <ul class="mb-0">
                                    <li><strong>JSON格式:</strong> 浏览器扩展导出的JSON文件内容</li>
                                    <li><strong>Netscape格式:</strong> 标准的cookies.txt格式</li>
                                    <li><strong>自动检测:</strong> 系统会自动识别格式并转换</li>
                                </ul>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cookiesFormat" class="form-label">Cookies格式</label>
                                    <select class="form-select" id="cookiesFormat">
                                        <option value="auto">自动检测</option>
                                        <option value="json">JSON格式</option>
                                        <option value="netscape">Netscape格式</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">快速操作</label>
                                    <div>
                                        <button id="clearContent" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-trash"></i> 清空
                                        </button>
                                        <button id="pasteExample" class="btn btn-outline-info btn-sm ms-2">
                                            <i class="fas fa-paste"></i> 示例
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="cookiesContent" class="form-label">Cookies内容</label>
                                <textarea class="form-control font-monospace" id="cookiesContent" rows="12" 
                                          placeholder="粘贴您的cookies内容...

支持格式：
1. JSON格式 (浏览器扩展导出)
2. Netscape格式 (cookies.txt)

示例 JSON 格式：
[{&quot;domain&quot;: &quot;.youtube.com&quot;, &quot;name&quot;: &quot;SID&quot;, &quot;value&quot;: &quot;...&quot;}]

示例 Netscape 格式：
# Netscape HTTP Cookie File
.youtube.com	TRUE	/	FALSE	1234567890	SID	value"></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="form-text">
                                    <small>
                                        <strong>完整备份模式</strong>：自动备份所有cookies并按平台分离到独立文件<br>
                                        <small class="text-muted">
                                            支持平台：YouTube、Twitter/X、Instagram、TikTok、Bilibili等<br>
                                            下载时系统根据URL智能选择对应平台的cookies文件
                                        </small>
                                    </small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <button id="importCookies" class="btn btn-success">
                                        <i class="fas fa-upload"></i> 导入Cookies
                                    </button>
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted">导入前会自动备份现有cookies</small>
                                </div>
                            </div>

                            <div id="importResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>

                <!-- Cookies管理标签页 -->
                <div class="tab-pane fade" id="manage" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs"></i> Cookies管理</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <button id="refreshCookiesList" class="btn btn-primary">
                                        <i class="fas fa-sync-alt"></i> 刷新列表
                                    </button>
                                    <button id="testAllCookies" class="btn btn-success ms-2">
                                        <i class="fas fa-check-circle"></i> 测试所有平台
                                    </button>
                                </div>
                                <small class="text-muted">Cookies文件保存在: /app/config/</small>
                            </div>

                            <div id="cookiesList">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载Cookies列表...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 获取指南标签页 -->
                <div class="tab-pane fade" id="help" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle"></i> Cookies获取指南</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6><i class="fab fa-youtube"></i> YouTube Cookies</h6>
                                    <ol>
                                        <li>安装扩展: "Get cookies.txt LOCALLY"</li>
                                        <li>访问并登录 <a href="https://youtube.com" target="_blank">YouTube</a></li>
                                        <li>点击扩展图标</li>
                                        <li>选择 "youtube.com"</li>
                                        <li>复制JSON内容到导入框</li>
                                    </ol>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fab fa-twitter"></i> Twitter/X Cookies</h6>
                                    <ol>
                                        <li>安装扩展: "Get cookies.txt LOCALLY"</li>
                                        <li>访问并登录 <a href="https://x.com" target="_blank">X (Twitter)</a></li>
                                        <li>点击扩展图标</li>
                                        <li>选择 "x.com"</li>
                                        <li>复制JSON内容到导入框</li>
                                    </ol>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-globe"></i> 多平台智能管理</h6>
                                    <ol>
                                        <li>导入任意平台的cookies</li>
                                        <li>系统按平台自动分离到独立文件</li>
                                        <li>下载时根据URL智能选择对应文件</li>
                                        <li>一次导入，支持所有平台</li>
                                        <li>避免平台间cookies冲突</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <h6><i class="fas fa-exclamation-triangle"></i> 重要提醒</h6>
                                <ul class="mb-0">
                                    <li>系统为每个平台生成独立cookies文件</li>
                                    <li>下载时根据URL智能选择对应平台文件</li>
                                    <li>导入前会自动备份现有cookies</li>
                                    <li>支持格式自动检测和转换</li>
                                    <li>导入后会自动测试有效性</li>
                                    <li>建议每3个月更新一次</li>
                                    <li>保持账号活跃状态（各平台）</li>
                                </ul>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt"></i> 安全说明</h6>
                                <ul class="mb-0">
                                    <li>Cookies按平台分别存储在独立文件中</li>
                                    <li>不会存储您的密码信息</li>
                                    <li>您可以随时更新或删除cookies</li>
                                    <li>建议使用专门的账号进行下载</li>
                                    <li>智能选择对应平台cookies，避免冲突</li>
                                    <li>便于单独管理和备份各平台cookies</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 页面加载时检查状态
    checkCookiesStatus();
    loadCookiesList();

    // 刷新状态按钮
    document.getElementById('refreshStatus').addEventListener('click', function() {
        checkCookiesStatus();
        loadCookiesList();
    });



    // 导入相关按钮
    document.getElementById('importCookies').addEventListener('click', importCookies);

    // Cookies管理相关按钮
    document.getElementById('refreshPlatforms').addEventListener('click', loadCookiesList);
    document.getElementById('refreshCookiesList').addEventListener('click', loadCookiesList);
    document.getElementById('testAllCookies').addEventListener('click', testAllPlatformCookies);

    // 快速操作按钮
    document.getElementById('clearContent').addEventListener('click', function() {
        document.getElementById('cookiesContent').value = '';
        showInfo('已清空内容');
    });

    document.getElementById('pasteExample').addEventListener('click', function() {
        const exampleContent = `[
    {
        "domain": ".youtube.com",
        "name": "SID",
        "value": "your_sid_value_here",
        "path": "/",
        "expires": 1735689600,
        "httpOnly": false,
        "secure": true
    },
    {
        "domain": ".youtube.com",
        "name": "HSID",
        "value": "your_hsid_value_here",
        "path": "/",
        "expires": 1735689600,
        "httpOnly": false,
        "secure": true
    }
]`;
        document.getElementById('cookiesContent').value = exampleContent;
        showInfo('已粘贴示例内容，请替换为您的实际cookies');
    });

    // 检查cookies状态
    function checkCookiesStatus() {
        fetch('/api/cookies/status', {
            credentials: 'same-origin'  // 使用cookies认证
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatusDisplay(data);
            } else {
                showError('检查状态失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('检查状态失败: ' + error.message);
        });
    }

    // 更新状态显示
    function updateStatusDisplay(status) {
        const statusBadge = document.getElementById('cookiesStatusBadge');
        const statusText = document.getElementById('cookiesStatusText');

        let badgeClass = 'bg-secondary';
        let statusMessage = '未知状态';

        if (status.exists) {
            switch (status.status) {
                case 'good':
                    badgeClass = 'bg-success';
                    statusMessage = '状态良好';
                    break;
                case 'warning':
                    badgeClass = 'bg-warning';
                    statusMessage = '需要关注';
                    break;
                case 'expired':
                    badgeClass = 'bg-danger';
                    statusMessage = '可能过期';
                    break;
                case 'incomplete':
                    badgeClass = 'bg-warning';
                    statusMessage = '内容不完整';
                    break;
            }
        } else {
            badgeClass = 'bg-danger';
            statusMessage = '不存在';
        }

        statusBadge.innerHTML = `<span class="badge ${badgeClass}">${statusMessage}</span>`;

        // 构建状态文本，包含平台信息
        let statusTextContent = status.message || '无详细信息';

        // 添加平台信息
        if (status.platform_analysis && Object.keys(status.platform_analysis).length > 0) {
            const platforms = Object.keys(status.platform_analysis);
            const platformBadges = platforms.map(platform => {
                const analysis = status.platform_analysis[platform];
                const badgeClass = analysis.has_auth ? 'bg-success' : 'bg-warning';
                const icon = getPlatformIcon(platform);
                const completeness = Math.round(analysis.completeness * 100);
                return `<span class="badge ${badgeClass} me-1" title="${platform}: ${completeness}% 完整度">${icon} ${platform}</span>`;
            }).join('');
            statusTextContent += `<br><small>支持平台: ${platformBadges}</small>`;
        }

        statusText.innerHTML = statusTextContent;
    }

    // 获取平台图标
    function getPlatformIcon(platform) {
        const icons = {
            'youtube': '📺',
            'twitter': '🐦',
            'instagram': '📷',
            'tiktok': '🎵',
            'bilibili': '📺'
        };
        return icons[platform] || '🌐';
    }















    // 导入cookies
    function importCookies() {
        const content = document.getElementById('cookiesContent').value.trim();

        if (!content) {
            showError('请先输入cookies内容');
            return;
        }

        const btn = document.getElementById('importCookies');
        btn.disabled = true;
        btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 导入中 (完整备份模式)`;

        fetch('/api/cookies/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',  // 使用cookies认证
            body: JSON.stringify({
                cookies_content: content,
                format: 'auto'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`Cookies导入成功！(完整备份模式) 找到 ${data.found_cookies.length} 个重要cookies`);
                document.getElementById('cookiesContent').value = '';
                checkCookiesStatus(); // 刷新状态

                // 显示测试结果
                if (data.test_result) {
                    if (data.test_result.valid) {
                        showSuccess('导入的cookies测试通过，可以正常使用');
                    } else {
                        showWarning('导入成功但测试失败，可能需要重新获取cookies');
                    }
                }
            } else {
                showError('导入失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('导入失败: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-upload"></i> 导入Cookies';
        });
    }



    // 加载cookies列表
    function loadCookiesList() {
        fetch('/api/cookies/list', {
            credentials: 'same-origin'  // 使用cookies认证
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCookiesList(data.cookies_files);
                document.getElementById('platformCount').innerHTML =
                    `<span class="badge bg-success">${data.cookies_files.length}</span>`;
            } else {
                showError('加载Cookies列表失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('加载Cookies列表失败: ' + error.message);
        });
    }

    // 显示cookies列表
    function displayCookiesList(cookiesFiles) {
        const container = document.getElementById('cookiesList');

        if (cookiesFiles.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无导入的Cookies文件</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>平台</th><th>修改时间</th><th>Cookies数量</th><th>状态</th><th>操作</th></tr></thead><tbody>';

        cookiesFiles.forEach(file => {
            const statusBadge = file.has_auth ?
                '<span class="badge bg-success">有认证</span>' :
                '<span class="badge bg-warning">无认证</span>';

            const expiredInfo = file.expired_cookies > 0 ?
                `<small class="text-danger">(${file.expired_cookies}个已过期)</small>` : '';

            html += `
                <tr>
                    <td>
                        <strong>${file.platform}</strong><br>
                        <small class="text-muted">${file.file_name}</small>
                    </td>
                    <td>${file.modified_date}</td>
                    <td>
                        ${file.cookies_count} 个
                        ${expiredInfo}
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-info" onclick="inspectPlatformCookies('${file.platform}')" title="检查详情">
                                <i class="fas fa-search"></i> 检查
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testPlatformCookies('${file.platform}')" title="测试有效性">
                                <i class="fas fa-check"></i> 测试
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deletePlatformCookies('${file.platform}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // 显示备份列表
    function displayBackupsList(backups) {
        const container = document.getElementById('backupsList');
        
        if (backups.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无备份文件</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>文件名</th><th>创建时间</th><th>大小</th><th>操作</th></tr></thead><tbody>';

        backups.forEach(backup => {
            const sizeKB = (backup.file_size / 1024).toFixed(1);
            html += `
                <tr>
                    <td><code>${backup.filename}</code></td>
                    <td>${backup.created_time_str}</td>
                    <td>${sizeKB} KB</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-info" onclick="inspectBackup('${backup.filename}')" title="检查备份内容">
                                <i class="fas fa-search"></i> 检查
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="restoreBackup('${backup.filename}', false)" title="${backup.backup_type === 'smart' ? '智能恢复（模拟导入）' : '直接恢复'}">
                                <i class="fas fa-${backup.backup_type === 'smart' ? 'magic' : 'undo'}"></i> ${backup.backup_type === 'smart' ? '智能恢复' : '恢复'}
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.filename}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                        ${backup.backup_type === 'smart' ? '<br><small class="text-success"><i class="fas fa-star"></i> 支持智能恢复</small>' : '<small class="text-muted">传统备份</small>'}
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // 检查备份文件
    window.inspectBackup = function(filename) {
        fetch('/api/cookies/backup/inspect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                backup_filename: filename
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showBackupInspectionModal(data);
            } else {
                showError('检查备份失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('检查备份失败: ' + error.message);
        });
    };

    // 显示备份检查结果
    function showBackupInspectionModal(data) {
        const isComplete = data.is_complete;
        const completenessIcon = isComplete ? '✅' : '⚠️';
        const completenessText = isComplete ? '完整' : '不完整';

        let inspectionInfo = `🔍 **备份文件检查报告**\n\n`;
        inspectionInfo += `📁 **文件信息:**\n`;
        inspectionInfo += `- 文件名: ${data.filename}\n`;
        inspectionInfo += `- 文件大小: ${(data.file_size / 1024).toFixed(1)} KB\n`;
        inspectionInfo += `- 修改时间: ${data.modified_time}\n`;
        inspectionInfo += `- 有效行数: ${data.valid_lines}\n`;

        inspectionInfo += `\n🔑 **重要Cookies分析:**\n`;
        inspectionInfo += `- 完整性: ${completenessIcon} ${completenessText}\n`;
        inspectionInfo += `- 重要cookies数量: ${data.important_cookies_count}/${data.important_cookies_total || 19}\n`;
        inspectionInfo += `- 找到的cookies: ${data.important_cookies.join(', ') || '无'}\n`;

        inspectionInfo += `\n📊 **详细分析:**\n`;
        inspectionInfo += `- SID (会话ID): ${data.analysis.has_sid ? '✅' : '❌'}\n`;
        inspectionInfo += `- HSID (HTTP会话ID): ${data.analysis.has_hsid ? '✅' : '❌'}\n`;
        inspectionInfo += `- SSID (安全会话ID): ${data.analysis.has_ssid ? '✅' : '❌'}\n`;
        inspectionInfo += `- APISID (API会话ID): ${data.analysis.has_apisid ? '✅' : '❌'}\n`;
        inspectionInfo += `- SAPISID (安全API会话ID): ${data.analysis.has_sapisid ? '✅' : '❌'}\n`;
        inspectionInfo += `- LOGIN_INFO (登录信息): ${data.analysis.has_login_info ? '✅' : '❌'}\n`;
        inspectionInfo += `- VISITOR_INFO1_LIVE (访客信息): ${data.analysis.has_visitor_info ? '✅' : '❌'}\n`;

        // 添加过期时间分析
        if (data.expiration_analysis) {
            const exp = data.expiration_analysis;
            inspectionInfo += `\n⏰ **过期时间分析:**\n`;
            inspectionInfo += `- 总cookies数: ${exp.total_cookies}\n`;
            inspectionInfo += `- 有效cookies: ${exp.valid_cookies}\n`;
            inspectionInfo += `- 已过期cookies: ${exp.expired_cookies}\n`;

            if (exp.overall_status) {
                let statusIcon = '❓';
                let statusText = exp.overall_status;

                switch(exp.overall_status) {
                    case 'excellent':
                        statusIcon = '🟢';
                        statusText = '优秀 (90天以上)';
                        break;
                    case 'good':
                        statusIcon = '🟡';
                        statusText = '良好 (30-90天)';
                        break;
                    case 'expiring_soon':
                        statusIcon = '🟠';
                        statusText = '即将过期 (<30天)';
                        break;
                    case 'mostly_expired':
                        statusIcon = '🔴';
                        statusText = '大部分已过期';
                        break;
                    case 'all_expired':
                        statusIcon = '❌';
                        statusText = '全部已过期';
                        break;
                }

                inspectionInfo += `- 整体状态: ${statusIcon} ${statusText}\n`;
            }

            if (exp.min_remaining_days !== undefined && exp.max_remaining_days !== undefined) {
                inspectionInfo += `- 剩余时间范围: ${Math.floor(exp.min_remaining_days)} - ${Math.floor(exp.max_remaining_days)} 天\n`;
            }

            if (exp.earliest_expiry && exp.latest_expiry) {
                inspectionInfo += `- 最早过期: ${exp.earliest_expiry}\n`;
                inspectionInfo += `- 最晚过期: ${exp.latest_expiry}\n`;
            }

            // 显示具体cookies的过期时间
            if (exp.cookies_details && Object.keys(exp.cookies_details).length > 0) {
                inspectionInfo += `\n📅 **具体过期时间:**\n`;
                Object.entries(exp.cookies_details).forEach(([cookieName, details]) => {
                    const status = details.is_expired ? '❌ 已过期' : `✅ ${Math.floor(details.remaining_days)}天`;
                    inspectionInfo += `- ${cookieName}: ${status} (${details.expiry_date})\n`;
                });
            }
        }

        inspectionInfo += `\n💡 **建议:**\n`;
        if (isComplete) {
            // 根据过期时间分析给出更精确的建议
            if (data.expiration_analysis && data.expiration_analysis.overall_status) {
                const status = data.expiration_analysis.overall_status;
                if (status === 'all_expired' || status === 'mostly_expired') {
                    inspectionInfo += `- ⚠️ 此备份中的cookies大部分已过期\n`;
                    inspectionInfo += `- 🔄 建议重新获取新的cookies而不是恢复此备份\n`;
                } else if (status === 'expiring_soon') {
                    inspectionInfo += `- ⚠️ 此备份中的cookies即将过期（<30天）\n`;
                    inspectionInfo += `- 🎯 可以临时恢复使用，但建议尽快更新cookies\n`;
                } else {
                    inspectionInfo += `- ✅ 此备份包含完整且有效的认证cookies\n`;
                    inspectionInfo += `- 🎯 建议使用此备份恢复cookies功能\n`;
                    if (status === 'excellent') {
                        inspectionInfo += `- 🌟 cookies有效期充足，可以长期使用\n`;
                    }
                }
            } else {
                inspectionInfo += `- ✅ 此备份包含完整的认证cookies，可以安全恢复\n`;
                inspectionInfo += `- 🎯 建议使用此备份恢复cookies功能\n`;
            }
        } else {
            inspectionInfo += `- ⚠️ 此备份缺少重要的认证cookies\n`;
            inspectionInfo += `- 🚫 不建议恢复此备份，可能无法正常使用\n`;
            inspectionInfo += `- 💡 建议重新获取完整的cookies\n`;
        }

        // 创建检查结果模态框
        const modalHtml = `
            <div class="modal fade" id="inspectionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔍 备份文件检查报告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${inspectionInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            ${isComplete ?
                                `<button type="button" class="btn btn-success" onclick="restoreBackupFromModal('${data.filename}')">恢复此备份</button>` :
                                `<button type="button" class="btn btn-outline-secondary" disabled>不建议恢复</button>`
                            }
                            <button type="button" class="btn btn-primary" onclick="copyInspectionInfo()">复制报告</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('inspectionModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('inspectionModal'));
        modal.show();

        // 存储检查信息供复制使用
        window.currentInspectionInfo = inspectionInfo;
    }

    // 从模态框恢复备份
    window.restoreBackupFromModal = function(filename) {
        // 关闭检查模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('inspectionModal'));
        if (modal) {
            modal.hide();
        }

        // 执行恢复
        setTimeout(() => {
            restoreBackup(filename, true); // 安全恢复
        }, 300);
    };

    // 复制检查信息
    window.copyInspectionInfo = function() {
        if (window.currentInspectionInfo) {
            navigator.clipboard.writeText(window.currentInspectionInfo).then(() => {
                showSuccess('检查报告已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 显示恢复对比报告
    function showRestoreComparisonModal(data) {
        const comparison = data.comparison;
        const beforeCookies = data.before_cookies || [];
        const afterCookies = data.after_cookies || [];
        const backupCookies = data.backup_cookies || [];

        let comparisonInfo = `🔄 **Cookies恢复对比报告**\n\n`;

        comparisonInfo += `📊 **恢复概况:**\n`;
        comparisonInfo += `- 恢复文件: ${data.restored_from}\n`;
        comparisonInfo += `- 恢复状态: ${comparison.restoration_success ? '✅ 完全成功' : '⚠️ 部分成功'}\n`;
        comparisonInfo += `- 整体改善: ${comparison.improvement ? '✅ 是' : '❌ 否'}\n`;

        comparisonInfo += `\n📈 **数量对比:**\n`;
        comparisonInfo += `- 恢复前: ${beforeCookies.length} 个重要cookies\n`;
        comparisonInfo += `- 备份中: ${backupCookies.length} 个重要cookies\n`;
        comparisonInfo += `- 恢复后: ${afterCookies.length} 个重要cookies\n`;

        comparisonInfo += `\n🔍 **详细变化:**\n`;

        if (comparison.added.length > 0) {
            comparisonInfo += `✅ **新增cookies (${comparison.added.length}个):**\n`;
            comparison.added.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        } else {
            comparisonInfo += `❌ **新增cookies:** 无\n`;
        }

        if (comparison.lost.length > 0) {
            comparisonInfo += `\n❌ **丢失cookies (${comparison.lost.length}个):**\n`;
            comparison.lost.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        } else {
            comparisonInfo += `\n✅ **丢失cookies:** 无\n`;
        }

        if (comparison.kept.length > 0) {
            comparisonInfo += `\n🔄 **保持cookies (${comparison.kept.length}个):**\n`;
            comparison.kept.forEach(cookie => {
                comparisonInfo += `  • ${cookie}\n`;
            });
        }

        comparisonInfo += `\n📋 **完整列表对比:**\n`;
        comparisonInfo += `- 恢复前: [${beforeCookies.join(', ') || '无'}]\n`;
        comparisonInfo += `- 备份中: [${backupCookies.join(', ') || '无'}]\n`;
        comparisonInfo += `- 恢复后: [${afterCookies.join(', ') || '无'}]\n`;

        comparisonInfo += `\n💡 **结果分析:**\n`;
        if (comparison.restoration_success) {
            comparisonInfo += `- ✅ 恢复完全成功，备份中的所有cookies都已正确恢复\n`;
        } else {
            comparisonInfo += `- ⚠️ 恢复不完全，可能存在以下问题:\n`;
            comparisonInfo += `  • 备份文件可能已损坏\n`;
            comparisonInfo += `  • 文件权限问题\n`;
            comparisonInfo += `  • 格式转换问题\n`;
        }

        if (comparison.improvement) {
            comparisonInfo += `- 🎯 恢复后cookies数量增加，功能应该有所改善\n`;
        } else {
            comparisonInfo += `- 📉 恢复后cookies数量未增加，可能需要重新获取新的cookies\n`;
        }

        // 创建对比报告模态框
        const modalHtml = `
            <div class="modal fade" id="comparisonModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔄 Cookies恢复对比报告</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${comparisonInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-info" onclick="testCookiesAfterRestore()">测试恢复效果</button>
                            <button type="button" class="btn btn-primary" onclick="copyComparisonInfo()">复制报告</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('comparisonModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('comparisonModal'));
        modal.show();

        // 存储对比信息供复制使用
        window.currentComparisonInfo = comparisonInfo;
    }

    // 恢复后测试cookies
    window.testCookiesAfterRestore = function() {
        // 关闭对比模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('comparisonModal'));
        if (modal) {
            modal.hide();
        }

        // 执行测试
        setTimeout(() => {
            testCookiesValidity();
        }, 300);
    };

    // 复制对比信息
    window.copyComparisonInfo = function() {
        if (window.currentComparisonInfo) {
            navigator.clipboard.writeText(window.currentComparisonInfo).then(() => {
                showSuccess('对比报告已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 删除备份文件
    window.deleteBackup = function(filename) {
        if (!confirm(`确定要删除备份文件 "${filename}" 吗？\n\n此操作不可恢复！`)) {
            return;
        }

        fetch('/api/cookies/backup/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',  // 使用cookies认证
            body: JSON.stringify({
                backup_filename: filename
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('备份文件已删除: ' + filename);
                loadBackupsList(); // 刷新备份列表
            } else {
                showError('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('删除失败: ' + error.message);
        });
    };

    // 恢复备份
    window.restoreBackup = function(filename, createBackup = false) {
        let confirmMessage;
        if (createBackup) {
            confirmMessage = `确定要安全恢复备份 "${filename}" 吗？\n\n这将：\n1. 先备份当前cookies\n2. 然后恢复选择的备份`;
        } else {
            confirmMessage = `确定要直接恢复备份 "${filename}" 吗？\n\n⚠️ 这将直接替换当前cookies，不会创建备份！`;
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        fetch('/api/cookies/backup/restore', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                backup_filename: filename,
                create_backup: createBackup
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示基本成功消息
                showSuccess('✅ ' + data.message);

                // 如果有对比数据，显示详细的恢复报告
                if (data.comparison) {
                    setTimeout(() => {
                        showRestoreComparisonModal(data);
                    }, 1000);
                }

                checkCookiesStatus(); // 刷新状态
                loadBackupsList(); // 刷新备份列表
            } else {
                showError('❌ 恢复失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('❌ 恢复失败: ' + error.message);
        });
    };

    // 清理旧备份
    function cleanupOldBackups() {
        if (!confirm('确定要清理旧备份吗？\n\n这将删除除最新3个备份外的所有备份文件！')) {
            return;
        }

        fetch('/api/cookies/backup/list', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.backups.length > 3) {
                // 按时间排序，保留最新的3个
                const sortedBackups = data.backups.sort((a, b) => b.created_time - a.created_time);
                const toDelete = sortedBackups.slice(3); // 删除第4个及以后的

                if (toDelete.length === 0) {
                    showInfo('没有需要清理的旧备份');
                    return;
                }

                // 批量删除
                let deleteCount = 0;
                let totalCount = toDelete.length;

                showInfo(`开始清理 ${totalCount} 个旧备份...`);

                toDelete.forEach((backup, index) => {
                    fetch('/api/cookies/backup/delete', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'same-origin',
                        body: JSON.stringify({ backup_filename: backup.filename })
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            deleteCount++;
                        }

                        // 如果是最后一个
                        if (index === totalCount - 1) {
                            showSuccess(`✅ 清理完成！删除了 ${deleteCount}/${totalCount} 个旧备份`);
                            loadBackupsList(); // 刷新列表
                        }
                    })
                    .catch(error => {
                        console.error('删除备份失败:', error);
                        if (index === totalCount - 1) {
                            showWarning(`⚠️ 清理完成，但有 ${totalCount - deleteCount} 个文件删除失败`);
                            loadBackupsList();
                        }
                    });
                });
            } else {
                showInfo('备份文件不超过3个，无需清理');
            }
        })
        .catch(error => {
            showError('获取备份列表失败: ' + error.message);
        });
    }

    // 检查平台cookies
    window.inspectPlatformCookies = function(platform) {
        fetch('/api/cookies/inspect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                platform: platform
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPlatformInspectionModal(data);
            } else {
                showError('检查失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('检查失败: ' + error.message);
        });
    };

    // 测试平台cookies
    window.testPlatformCookies = function(platform) {
        showInfo(`正在测试 ${platform} 平台cookies...`);

        fetch('/api/cookies/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                platform: platform
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.valid) {
                    showSuccess(`✅ ${platform} 平台cookies有效: ${data.message}`);
                } else {
                    showWarning(`⚠️ ${platform} 平台cookies可能无效: ${data.message}`);
                }
            } else {
                showError(`❌ ${platform} 平台测试失败: ${data.error}`);
            }
        })
        .catch(error => {
            showError(`❌ ${platform} 平台测试失败: ${error.message}`);
        });
    };

    // 删除平台cookies
    window.deletePlatformCookies = function(platform) {
        if (!confirm(`确定要删除 ${platform} 平台的cookies文件吗？\n\n此操作不可恢复！`)) {
            return;
        }

        fetch('/api/cookies/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                platform: platform
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`✅ ${platform} 平台cookies已删除`);
                loadCookiesList(); // 刷新列表
                checkCookiesStatus(); // 刷新状态
            } else {
                showError('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('删除失败: ' + error.message);
        });
    };

    // 测试所有平台cookies
    function testAllPlatformCookies() {
        showInfo('正在测试所有平台cookies...');

        fetch('/api/cookies/test', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = '🧪 所有平台测试完成:\n\n';

                if (data.platform_results) {
                    Object.entries(data.platform_results).forEach(([platform, result]) => {
                        const status = result.valid ? '✅' : '❌';
                        message += `${status} ${platform}: ${result.message}\n`;
                    });
                } else {
                    message += data.valid ? '✅ 测试通过' : '❌ 测试失败';
                    message += `\n消息: ${data.message}`;
                }

                showInfo(message);
            } else {
                showError('测试失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('测试失败: ' + error.message);
        });
    }

    // 显示平台检查结果
    function showPlatformInspectionModal(data) {
        let inspectionInfo = `🔍 **${data.platform} 平台Cookies检查报告**\n\n`;

        inspectionInfo += `📁 **文件信息:**\n`;
        inspectionInfo += `- 平台: ${data.platform}\n`;
        inspectionInfo += `- 文件大小: ${(data.file_size / 1024).toFixed(1)} KB\n`;
        inspectionInfo += `- 总行数: ${data.total_lines}\n`;
        inspectionInfo += `- 有效行数: ${data.valid_lines}\n`;

        if (data.analysis) {
            inspectionInfo += `\n📊 **Cookies分析:**\n`;
            inspectionInfo += `- 总数量: ${data.analysis.total_cookies}\n`;
            inspectionInfo += `- 有效: ${data.analysis.valid_cookies}\n`;
            inspectionInfo += `- 过期: ${data.analysis.expired_cookies}\n`;
            inspectionInfo += `- 状态: ${data.analysis.overall_status}\n`;
        }

        if (data.cookies_details && data.cookies_details.length > 0) {
            inspectionInfo += `\n🍪 **重要Cookies详情:**\n`;
            data.cookies_details.slice(0, 10).forEach(cookie => {
                const status = cookie.is_expired ? '❌' : '✅';
                const important = cookie.is_important ? '⭐' : '';
                inspectionInfo += `${status}${important} ${cookie.name}: ${cookie.domain}\n`;
            });

            if (data.cookies_details.length > 10) {
                inspectionInfo += `... 还有 ${data.cookies_details.length - 10} 个cookies\n`;
            }
        }

        // 创建检查结果模态框
        const modalHtml = `
            <div class="modal fade" id="platformInspectionModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">🔍 ${data.platform} 平台Cookies检查</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${inspectionInfo}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-success" onclick="testPlatformCookies('${data.platform}')">测试此平台</button>
                            <button type="button" class="btn btn-primary" onclick="copyPlatformInspectionInfo()">复制报告</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('platformInspectionModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('platformInspectionModal'));
        modal.show();

        // 存储检查信息供复制使用
        window.currentPlatformInspectionInfo = inspectionInfo;
    }

    // 复制平台检查信息
    window.copyPlatformInspectionInfo = function() {
        if (window.currentPlatformInspectionInfo) {
            navigator.clipboard.writeText(window.currentPlatformInspectionInfo).then(() => {
                showSuccess('检查报告已复制到剪贴板');
            }).catch(() => {
                showWarning('复制失败，请手动选择文本复制');
            });
        }
    };

    // 消息显示函数
    function showSuccess(message) {
        showMessage(message, 'success');
    }

    function showError(message) {
        showMessage(message, 'danger');
    }

    function showWarning(message) {
        showMessage(message, 'warning');
    }

    function showInfo(message) {
        showMessage(message, 'info');
    }

    function showMessage(message, type) {
        const result = document.getElementById('importResult');
        result.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    }
});
</script>
{% endblock %}
