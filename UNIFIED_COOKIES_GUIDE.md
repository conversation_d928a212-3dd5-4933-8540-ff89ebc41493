# 统一Cookies管理系统指南

## 🎯 系统概述

本系统实现了统一cookies文件管理，完美解决了多平台cookies管理的复杂性：

### 核心理念
- **一个文件，所有平台**：所有平台cookies存储在统一文件中
- **yt-dlp智能选择**：yt-dlp自动从统一文件中选择对应平台的cookies
- **智能检测记录**：系统智能检测平台并记录详细日志
- **无需手动配置**：用户无需关心平台切换，系统自动处理

## 🚀 工作原理

### 1. 统一文件结构
```
# Netscape HTTP Cookie File
# 统一cookies文件包含所有平台

# YouTube cookies
.youtube.com    TRUE    /    TRUE    1735689600    SID    youtube_sid_value
.google.com     TRUE    /    TRUE    1735689600    HSID   youtube_hsid_value

# Twitter/X cookies  
.x.com          TRUE    /    TRUE    1735689600    auth_token    twitter_auth_value
.twitter.com    TRUE    /    TRUE    1735689600    ct0           twitter_ct0_value

# Instagram cookies
.instagram.com  TRUE    /    TRUE    1735689600    sessionid     instagram_session_value

# 其他平台cookies...
```

### 2. 智能工作流程
```
用户下载 → 系统检测URL平台 → yt-dlp读取统一文件 → 自动使用对应平台cookies
```

### 3. 平台检测示例
- `https://youtube.com/watch?v=xxx` → 检测为 `youtube` 平台
- `https://x.com/user/status/xxx` → 检测为 `twitter` 平台  
- `https://instagram.com/p/xxx` → 检测为 `instagram` 平台

## 📋 支持的平台

| 平台 | 域名 | 重要Cookies | 认证Cookies |
|------|------|-------------|-------------|
| **YouTube** | youtube.com, google.com | SID, HSID, SSID, APISID, SAPISID, LOGIN_INFO, VISITOR_INFO1_LIVE | SID, __Secure-1PSID, __Secure-3PSID |
| **Twitter/X** | twitter.com, x.com | auth_token, ct0, guest_id, personalization_id, gt, twid | auth_token, ct0 |
| **Instagram** | instagram.com | sessionid, csrftoken, ds_user_id, ig_did, ig_nrcb | sessionid |
| **TikTok** | tiktok.com | sessionid, sid_tt, uid_tt, sid_guard, uid_guard, ssid_ucp_v1 | sessionid, sid_tt |
| **Bilibili** | bilibili.com | SESSDATA, bili_jct, DedeUserID, DedeUserID__ckMd5, sid | SESSDATA |

## 🛠️ 使用方法

### 1. 获取Cookies（任意平台）

#### 推荐方法
1. 安装浏览器扩展："Get cookies.txt LOCALLY"
2. 登录任意目标网站（YouTube、Twitter、Instagram等）
3. 点击扩展图标，选择对应域名
4. 复制JSON格式的cookies

#### 多平台一次性获取
1. 在同一浏览器中登录多个平台
2. 分别导出各平台cookies
3. 使用"完整备份模式"一次性导入所有cookies

### 2. 导入到统一文件

1. **访问管理页面**：`/admin/cookies-manager`

2. **选择导入模式**：
   - ✅ **智能提取模式**：自动识别并提取支持平台的cookies
   - ✅ **完整备份模式**：导入所有cookies（推荐多平台用户）

3. **导入操作**：
   - 粘贴JSON格式的cookies到文本框
   - 系统自动生成统一cookies文件
   - 包含所有平台的cookies

4. **验证结果**：
   - 系统显示支持的平台和认证状态
   - 例如：📺 youtube ✅ 🐦 twitter ✅ 📷 instagram ⚠️

### 3. 自动智能下载

导入后，下载任意平台视频：
- 系统自动检测URL平台
- yt-dlp自动从统一文件中选择对应cookies
- 无需手动配置或切换

## 🔍 智能日志系统

### 平台检测日志
```
🎯 检测到 twitter 平台，cookies包含认证信息: https://x.com/user/status/123
🎯 检测到 youtube 平台，cookies包含认证信息: https://youtube.com/watch?v=123
⚠️ 检测到 instagram 平台，但缺少认证cookies，可能影响下载: https://instagram.com/p/123
🔍 未识别平台，使用通用cookies文件: https://example.com/video
```

### 下载日志
```
✅ Cookies文件验证通过，yt-dlp将自动使用对应平台的cookies
🍪 使用统一cookies文件: /path/to/universal_cookies.txt
```

## 🎯 解决的具体问题

### ✅ Twitter/X NSFW内容
**问题**：`ERROR: [twitter] NSFW tweet requires authentication`

**解决方案**：
1. 使用能查看NSFW内容的Twitter账号获取cookies
2. 导入包含`auth_token`和`ct0`的cookies到统一文件
3. 下载Twitter URL时，yt-dlp自动使用Twitter cookies

### ✅ YouTube Bot检测
**问题**：`Sign in to confirm you're not a bot`

**解决方案**：
1. 使用活跃的YouTube账号获取cookies
2. 导入包含完整认证cookies到统一文件
3. 下载YouTube URL时，yt-dlp自动使用YouTube cookies

### ✅ 多平台统一管理
**问题**：不同平台需要不同的cookies配置

**解决方案**：
1. 一次导入多平台cookies到统一文件
2. yt-dlp根据URL自动选择对应平台cookies
3. 无需手动切换或配置

## 💡 系统优势

### 1. 用户体验
- **一次配置**：导入一次，支持所有平台
- **自动处理**：无需手动切换平台或配置
- **智能提示**：详细的平台检测和状态信息

### 2. 技术优势
- **统一存储**：一个文件包含所有平台cookies
- **yt-dlp原生支持**：利用yt-dlp的原生多域名cookies处理
- **无限扩展**：新平台只需添加配置，无需重写代码

### 3. 维护简单
- **统一备份**：一个文件的备份和恢复
- **状态监控**：统一的平台状态显示
- **日志清晰**：详细的平台检测和使用日志

## 🔧 技术实现

### 统一文件生成
```python
# 智能提取模式：提取所有支持平台的cookies
# 完整备份模式：转换所有cookies到统一格式
# 结果：生成包含多平台cookies的Netscape格式文件
```

### 智能平台检测
```python
def detect_platform_from_url(url):
    # 检查URL中的域名
    # 返回匹配的平台名称（youtube, twitter, instagram等）
```

### yt-dlp自动选择
```python
# yt-dlp读取统一cookies文件
# 根据目标URL的域名自动选择匹配的cookies
# 无需额外配置，原生支持
```

## 📊 状态监控

### 管理页面显示
- 🟢 **有认证**：平台cookies完整，可正常使用
- 🟡 **无认证**：平台cookies不完整，可能影响下载
- 📊 **完整度**：显示cookies完整度百分比
- 📺 **平台图标**：直观显示支持的平台

### 实时状态
```
支持平台: 📺 youtube ✅ 🐦 twitter ✅ 📷 instagram ⚠️ 🎵 tiktok ❌
```

## 🔄 维护建议

### 定期更新
- **正常使用**：每3个月更新一次cookies
- **频繁使用**：每月检查一次cookies状态
- **出现错误**：立即重新获取cookies

### 最佳实践
1. **专用账号**：建议使用专门的下载账号
2. **保持活跃**：定期使用账号保持活跃状态
3. **统一管理**：利用系统的统一备份功能
4. **多平台导入**：一次性导入多个平台的cookies

## 🚀 扩展支持

### 添加新平台
只需在配置中添加：
```python
'new_platform': {
    'domains': ['newsite.com', '.newsite.com'],
    'important_cookies': ['session_id', 'auth_token'],
    'auth_cookies': ['session_id']
}
```

### 自动适配
- 无需修改统一文件生成逻辑
- 无需重写yt-dlp调用代码
- 自动支持新平台的智能检测和日志记录

---

**更新时间**: 2025-06-02  
**版本**: 3.0  
**适用于**: yt-dlp-web-deploy 统一cookies管理系统
