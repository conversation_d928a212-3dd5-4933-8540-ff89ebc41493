#!/usr/bin/env python3
"""
检查多平台cookies系统的完整性
"""

import os
import sys
import importlib.util

def check_cookies_manager():
    """检查cookies管理器的完整性"""
    print("🔍 检查cookies管理器...")
    
    try:
        # 导入cookies管理器
        sys.path.append('webapp')
        from core.cookies_manager import CookiesManager
        
        # 创建实例
        manager = CookiesManager()
        
        # 检查必要的方法
        required_methods = [
            'get_platform_cookies_file',
            'get_all_platform_cookies_files', 
            'detect_platform_from_url',
            'get_cookies_for_url',
            'analyze_cookies_by_platform',
            'get_status',
            'import_cookies',
            'test_cookies'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(manager, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        
        # 检查平台配置
        if not hasattr(manager, 'platform_configs'):
            print("❌ 缺少平台配置")
            return False
        
        expected_platforms = {'youtube', 'twitter', 'instagram', 'tiktok', 'bilibili'}
        actual_platforms = set(manager.platform_configs.keys())
        
        if not expected_platforms.issubset(actual_platforms):
            missing_platforms = expected_platforms - actual_platforms
            print(f"❌ 缺少平台配置: {missing_platforms}")
            return False
        
        print("✅ cookies管理器检查通过")
        return True
        
    except Exception as e:
        print(f"❌ cookies管理器检查失败: {e}")
        return False

def check_download_manager():
    """检查下载管理器的完整性"""
    print("\n🔍 检查下载管理器...")
    
    try:
        from core.download_manager import DownloadManager
        
        # 创建实例
        manager = DownloadManager()
        
        # 检查必要的方法
        required_methods = [
            '_build_ytdlp_options',
            'download_video'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(manager, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        
        print("✅ 下载管理器检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 下载管理器检查失败: {e}")
        return False

def check_api_routes():
    """检查API路由的完整性"""
    print("\n🔍 检查API路由...")
    
    try:
        from routes.api import api_bp
        
        # 检查必要的路由
        required_routes = [
            '/cookies/status',
            '/cookies/import',
            '/cookies/test',
            '/download'
        ]
        
        # 获取所有路由
        routes = []
        for rule in api_bp.url_map.iter_rules():
            if rule.endpoint.startswith('api.'):
                routes.append(rule.rule)
        
        missing_routes = []
        for route in required_routes:
            if not any(route in r for r in routes):
                missing_routes.append(route)
        
        if missing_routes:
            print(f"❌ 缺少路由: {missing_routes}")
            return False
        
        print("✅ API路由检查通过")
        return True
        
    except Exception as e:
        print(f"❌ API路由检查失败: {e}")
        return False

def check_platform_consistency():
    """检查平台配置的一致性"""
    print("\n🔍 检查平台配置一致性...")
    
    try:
        from core.cookies_manager import CookiesManager
        
        manager = CookiesManager()
        
        # 检查每个平台的配置完整性
        for platform, config in manager.platform_configs.items():
            required_keys = ['domains', 'important_cookies', 'auth_cookies']
            
            for key in required_keys:
                if key not in config:
                    print(f"❌ {platform} 平台缺少配置: {key}")
                    return False
                
                if not config[key]:  # 检查是否为空
                    print(f"❌ {platform} 平台配置为空: {key}")
                    return False
            
            # 检查认证cookies是否在重要cookies中
            auth_cookies = set(config['auth_cookies'])
            important_cookies = set(config['important_cookies'])
            
            if not auth_cookies.issubset(important_cookies):
                missing_auth = auth_cookies - important_cookies
                print(f"❌ {platform} 平台认证cookies不在重要cookies中: {missing_auth}")
                return False
        
        print("✅ 平台配置一致性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 平台配置检查失败: {e}")
        return False

def check_file_structure():
    """检查文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        'webapp/core/cookies_manager.py',
        'webapp/core/download_manager.py',
        'webapp/routes/api.py',
        'webapp/templates/admin/cookies_manager.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    # 检查配置目录
    config_dir = 'webapp/config'
    if not os.path.exists(config_dir):
        print(f"❌ 配置目录不存在: {config_dir}")
        return False
    
    print("✅ 文件结构检查通过")
    return True

def check_method_signatures():
    """检查关键方法的签名"""
    print("\n🔍 检查方法签名...")
    
    try:
        from core.cookies_manager import CookiesManager
        import inspect
        
        manager = CookiesManager()
        
        # 检查关键方法的签名
        method_signatures = {
            'get_cookies_for_url': ['url'],
            'detect_platform_from_url': ['url'],
            'import_cookies': ['cookies_content'],
            'get_platform_cookies_file': ['platform']
        }
        
        for method_name, expected_params in method_signatures.items():
            if hasattr(manager, method_name):
                method = getattr(manager, method_name)
                sig = inspect.signature(method)
                actual_params = [p for p in sig.parameters.keys() if p != 'self']
                
                # 检查必需参数是否存在
                for param in expected_params:
                    if param not in actual_params:
                        print(f"❌ {method_name} 缺少参数: {param}")
                        return False
            else:
                print(f"❌ 缺少方法: {method_name}")
                return False
        
        print("✅ 方法签名检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🚀 开始多平台cookies系统完整性检查...\n")
    
    checks = [
        ("文件结构", check_file_structure),
        ("Cookies管理器", check_cookies_manager),
        ("下载管理器", check_download_manager),
        ("API路由", check_api_routes),
        ("平台配置一致性", check_platform_consistency),
        ("方法签名", check_method_signatures)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
            results.append((check_name, False))
    
    # 总结
    print(f"\n📊 完整性检查总结:")
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"- {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 多平台cookies系统完整性检查全部通过！")
        print("\n💡 系统特性:")
        print("  ✅ 支持多平台独立cookies文件")
        print("  ✅ 智能URL检测和文件选择")
        print("  ✅ 完整的有效性检测体系")
        print("  ✅ 跨平台状态监控")
        print("  ✅ 避免平台间cookies冲突")
        return True
    else:
        print("⚠️ 发现问题，需要修复后再次检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
